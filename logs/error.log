2025-06-08 16:01:44.091 [main] ERROR c.k.b.config.LoggingConfig - 这是一条ERROR级别的测试日志
2025-06-08 16:12:36.773 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

   bookController defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\controller\BookController.class]
┌─────┐
|  bookServiceImpl defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BookServiceImpl.class]
↑     ↓
|  borrowRecordServiceImpl defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BorrowRecordServiceImpl.class]
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

2025-06-08 16:21:15.313 [main] ERROR c.k.b.config.LoggingConfig - 这是一条ERROR级别的测试日志
