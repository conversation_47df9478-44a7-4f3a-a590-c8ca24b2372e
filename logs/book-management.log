2025-06-08 15:32:13.574 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 15:32:13.628 [main] INFO  c.k.b.BookManagementBackendApplication - Starting BookManagementBackendApplication using Java 21.0.6 with PID 37828 (D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes started by 86187 in D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM)
2025-06-08 15:32:13.629 [main] DEBUG c.k.b.BookManagementBackendApplication - Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-08 15:32:13.630 [main] INFO  c.k.b.BookManagementBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-08 15:32:14.524 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 15:32:14.526 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-08 15:32:14.558 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 JPA repository interfaces.
2025-06-08 15:32:14.572 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 15:32:14.573 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-08 15:32:14.598 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-08 15:32:14.692 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BookCategoryMapper.class]
2025-06-08 15:32:14.692 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BookMapper.class]
2025-06-08 15:32:14.692 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BorrowRecordMapper.class]
2025-06-08 15:32:14.692 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\UserMapper.class]
2025-06-08 15:32:14.693 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'bookCategoryMapper' and 'com.kyx.bookmanagementbackend.mapper.BookCategoryMapper' mapperInterface
2025-06-08 15:32:14.695 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'bookCategoryMapper'.
2025-06-08 15:32:14.695 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'bookMapper' and 'com.kyx.bookmanagementbackend.mapper.BookMapper' mapperInterface
2025-06-08 15:32:14.696 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'bookMapper'.
2025-06-08 15:32:14.696 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'borrowRecordMapper' and 'com.kyx.bookmanagementbackend.mapper.BorrowRecordMapper' mapperInterface
2025-06-08 15:32:14.697 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'borrowRecordMapper'.
2025-06-08 15:32:14.697 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.kyx.bookmanagementbackend.mapper.UserMapper' mapperInterface
2025-06-08 15:32:14.697 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-08 15:32:15.258 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-08 15:32:15.270 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-08 15:32:15.272 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-08 15:32:15.272 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-08 15:32:15.332 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-08 15:32:15.332 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1654 ms
2025-06-08 15:32:15.381 [main] DEBUG c.k.b.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-08 15:32:15.497 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-08 15:32:15.542 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-08 15:32:15.569 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-08 15:32:15.796 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-08 15:32:15.820 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-08 15:32:15.961 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a8b22b5
2025-06-08 15:32:15.963 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-08 15:32:16.020 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-08 15:32:16.036 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.33
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-08 15:32:16.321 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-08 15:32:16.325 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 15:32:16.420 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@50628080, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-08 15:32:16.569 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\mapper\BookMapper.xml]'
2025-06-08 15:32:16.601 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\mapper\BorrowRecordMapper.xml]'
2025-06-08 15:32:17.125 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-08 15:32:17.148 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: e2f5bbdf-1fbb-4276-b671-b4b5ca528168

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-08 15:32:17.155 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-08 15:32:17.413 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-08 15:32:17.863 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-08 15:32:17.881 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-08 15:32:17.889 [main] INFO  c.k.b.BookManagementBackendApplication - Started BookManagementBackendApplication in 4.789 seconds (process running for 5.725)
2025-06-08 15:32:17.977 [main] INFO  c.k.b.config.DataInitializer - 超级管理员账户已存在，跳过初始化
2025-06-08 15:32:42.956 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 15:32:42.956 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-08 15:32:42.958 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-08 15:32:42.980 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:32:43.026 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:32:43.151 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:32:43.299 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:32:43.299 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:32:43.464 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:32:43.466 [http-nio-8080-exec-3] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:32:43.466 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 167ms
2025-06-08 15:32:43.682 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:32:43.687 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:32:43.690 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:32:43.693 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:32:43.702 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:33:24.029 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:33:24.032 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:33:30.261 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:33:30.267 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:33:30.268 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:33:30.268 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:33:30.270 [http-nio-8080-exec-7] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:33:30.270 [http-nio-8080-exec-7] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:33:30.348 [http-nio-8080-exec-7] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:33:30.349 [http-nio-8080-exec-7] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:33:30.349 [http-nio-8080-exec-7] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 79ms
2025-06-08 15:33:30.414 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:33:30.421 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:33:30.424 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:33:30.425 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:33:30.425 [http-nio-8080-exec-9] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:33:31.998 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:33:31.999 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:33:57.084 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:33:57.089 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:33:57.090 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:33:57.090 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:33:57.092 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:33:57.092 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:33:57.172 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:33:57.172 [http-nio-8080-exec-3] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:33:57.173 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 81ms
2025-06-08 15:33:57.240 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:33:57.248 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:33:57.253 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:33:57.253 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:33:57.254 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:33:58.711 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 15:33:58.716 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 15:33:58.719 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 15:34:00.499 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 15:34:00.499 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 15:34:00.505 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 15:34:00.505 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 15:34:00.508 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 15:34:00.508 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 15:34:00.512 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 15:34:00.536 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 15:34:00.537 [http-nio-8080-exec-10] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 15:34:02.537 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:34:02.538 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:34:25.039 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:34:25.045 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:34:25.045 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:34:25.046 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:34:25.047 [http-nio-8080-exec-2] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:34:25.047 [http-nio-8080-exec-2] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:34:25.125 [http-nio-8080-exec-2] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:34:25.127 [http-nio-8080-exec-2] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:34:25.127 [http-nio-8080-exec-2] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 80ms
2025-06-08 15:34:25.203 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:34:25.209 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:34:25.214 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:34:25.216 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:34:25.217 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:34:36.669 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:34:36.670 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:35:01.087 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:35:01.091 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:35:01.091 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:35:01.092 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:35:01.092 [http-nio-8080-exec-9] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:35:01.093 [http-nio-8080-exec-9] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:35:01.182 [http-nio-8080-exec-9] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:35:01.183 [http-nio-8080-exec-9] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:35:01.183 [http-nio-8080-exec-9] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 91ms
2025-06-08 15:35:01.271 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:35:01.278 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:35:01.281 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:35:01.283 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:35:01.283 [http-nio-8080-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:35:03.135 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:35:03.136 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:39:54.972 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 15:39:54.983 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 15:39:54.987 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 15:41:05.519 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 15:41:05.530 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 15:41:05.532 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 15:41:34.342 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 15:41:34.342 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:45:47.938 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-08 15:45:47.948 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-08 15:45:47.965 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 15:45:47.967 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-08 15:45:47.975 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-08 15:45:54.151 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 15:45:54.194 [main] INFO  c.k.b.BookManagementBackendApplication - Starting BookManagementBackendApplication using Java 21.0.6 with PID 26492 (D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes started by 86187 in D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM)
2025-06-08 15:45:54.194 [main] DEBUG c.k.b.BookManagementBackendApplication - Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-08 15:45:54.194 [main] INFO  c.k.b.BookManagementBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-08 15:45:55.078 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 15:45:55.080 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-08 15:45:55.113 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 JPA repository interfaces.
2025-06-08 15:45:55.126 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 15:45:55.127 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-08 15:45:55.146 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-08 15:45:55.247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BookCategoryMapper.class]
2025-06-08 15:45:55.248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BookMapper.class]
2025-06-08 15:45:55.248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\BorrowRecordMapper.class]
2025-06-08 15:45:55.248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\mapper\UserMapper.class]
2025-06-08 15:45:55.248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'bookCategoryMapper' and 'com.kyx.bookmanagementbackend.mapper.BookCategoryMapper' mapperInterface
2025-06-08 15:45:55.252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'bookCategoryMapper'.
2025-06-08 15:45:55.252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'bookMapper' and 'com.kyx.bookmanagementbackend.mapper.BookMapper' mapperInterface
2025-06-08 15:45:55.252 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'bookMapper'.
2025-06-08 15:45:55.253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'borrowRecordMapper' and 'com.kyx.bookmanagementbackend.mapper.BorrowRecordMapper' mapperInterface
2025-06-08 15:45:55.253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'borrowRecordMapper'.
2025-06-08 15:45:55.253 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'com.kyx.bookmanagementbackend.mapper.UserMapper' mapperInterface
2025-06-08 15:45:55.254 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
2025-06-08 15:45:55.804 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-08 15:45:55.815 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-08 15:45:55.817 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-08 15:45:55.817 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-08 15:45:55.874 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-08 15:45:55.875 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1632 ms
2025-06-08 15:45:55.923 [main] DEBUG c.k.b.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-08 15:45:56.043 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-08 15:45:56.091 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-08 15:45:56.119 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-08 15:45:56.364 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-08 15:45:56.389 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-08 15:45:56.530 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a8b22b5
2025-06-08 15:45:56.532 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-08 15:45:56.582 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-08 15:45:56.600 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.33
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-08 15:45:56.872 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-08 15:45:56.877 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 15:45:56.979 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[PaginationInnerInterceptor(logger=org.apache.ibatis.logging.slf4j.Slf4jImpl@50628080, overflow=false, maxLimit=null, dbType=MYSQL, dialect=null, optimizeJoin=true)]}'
2025-06-08 15:45:57.134 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\mapper\BookMapper.xml]'
2025-06-08 15:45:57.167 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\mapper\BorrowRecordMapper.xml]'
2025-06-08 15:45:57.699 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-08 15:45:57.723 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d95f664b-414e-4d0f-8e72-f8c65825695a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-08 15:45:57.730 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-08 15:45:58.029 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-08 15:45:58.477 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-08 15:45:58.496 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-08 15:45:58.504 [main] INFO  c.k.b.BookManagementBackendApplication - Started BookManagementBackendApplication in 4.794 seconds (process running for 5.632)
2025-06-08 15:45:58.592 [main] INFO  c.k.b.config.DataInitializer - 超级管理员账户已存在，跳过初始化
2025-06-08 15:46:12.027 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 15:46:12.029 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-08 15:46:12.030 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-08 15:46:12.053 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/auth/login
2025-06-08 15:46:12.087 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/auth/login
2025-06-08 15:46:12.089 [http-nio-8080-exec-3] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/auth/login, Token存在: false
2025-06-08 15:46:12.091 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-08 15:46:12.093 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/auth/login
2025-06-08 15:46:12.253 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录请求 - 用户名: kyx666, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:46:12.254 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录验证开始 - 用户名: kyx666
2025-06-08 15:46:12.489 [http-nio-8080-exec-3] INFO  c.k.b.service.impl.UserServiceImpl - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 角色: SUPER_ADMIN
2025-06-08 15:46:12.491 [http-nio-8080-exec-3] INFO  c.k.b.utils.LogUtil - 用户操作 - 操作: 用户登录, 用户ID: 1, 用户名: kyx666, IP: 0:0:0:0:0:0:0:1, 参数: [角色:SUPER_ADMIN]
2025-06-08 15:46:12.492 [http-nio-8080-exec-3] INFO  c.k.b.controller.AuthController - 用户登录成功 - 用户名: kyx666, 用户ID: 1, 耗时: 239ms
2025-06-08 15:46:12.703 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:46:12.712 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:46:12.712 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 15:46:12.767 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:12.770 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:46:12.775 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:46:12.785 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:46:14.449 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 15:46:14.455 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:46:14.455 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 15:46:14.458 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:14.459 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 15:46:14.464 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 15:46:14.464 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 15:46:14.465 [http-nio-8080-exec-6] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:46:14.550 [http-nio-8080-exec-6] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 15:46:14.550 [http-nio-8080-exec-6] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 85ms, 参数: [1, 10]
2025-06-08 15:46:16.354 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 15:46:16.355 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 15:46:16.361 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 15:46:16.361 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 15:46:16.362 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 15:46:16.362 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 15:46:16.364 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:16.364 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:16.364 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 15:46:16.365 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 15:46:16.366 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 15:46:16.394 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 15:46:16.395 [http-nio-8080-exec-10] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 15:46:18.329 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 15:46:18.335 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 15:46:18.336 [http-nio-8080-exec-3] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/borrow/records, Token存在: true
2025-06-08 15:46:18.339 [http-nio-8080-exec-3] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:18.339 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 15:46:19.862 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 15:46:19.867 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 15:46:19.869 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 15:46:19.874 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:19.874 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 15:46:19.876 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:46:19.876 [http-nio-8080-exec-5] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 15:46:21.286 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 15:46:21.292 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 15:46:21.292 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 15:46:21.295 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:21.295 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 15:46:21.296 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 15:46:21.297 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 15:46:21.297 [http-nio-8080-exec-6] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 15:46:21.305 [http-nio-8080-exec-6] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 15:46:21.305 [http-nio-8080-exec-6] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 8ms, 参数: [1, 10]
2025-06-08 15:46:28.857 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 15:46:28.857 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 15:46:28.864 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 15:46:28.864 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 15:46:28.864 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 15:46:28.864 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 15:46:28.866 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:28.866 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 15:46:28.866 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 15:46:28.866 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 15:46:28.867 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 15:46:28.877 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 15:46:28.877 [http-nio-8080-exec-10] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 16:01:33.474 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-08 16:01:33.484 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-08 16:01:33.499 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:01:33.501 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-08 16:01:33.507 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-08 16:01:39.766 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 16:01:39.809 [main] INFO  c.k.b.BookManagementBackendApplication - Starting BookManagementBackendApplication using Java 21.0.6 with PID 38808 (D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes started by 86187 in D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM)
2025-06-08 16:01:39.810 [main] DEBUG c.k.b.BookManagementBackendApplication - Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-08 16:01:39.810 [main] INFO  c.k.b.BookManagementBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-08 16:01:40.711 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:01:40.713 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-08 16:01:40.746 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 JPA repository interfaces.
2025-06-08 16:01:40.758 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:01:40.760 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-08 16:01:40.778 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-08 16:01:41.442 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-08 16:01:41.453 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-08 16:01:41.455 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-08 16:01:41.455 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-08 16:01:41.512 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-08 16:01:41.514 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1660 ms
2025-06-08 16:01:41.565 [main] DEBUG c.k.b.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-08 16:01:41.679 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-08 16:01:41.724 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-08 16:01:41.751 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-08 16:01:41.982 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-08 16:01:42.008 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-08 16:01:42.140 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e67d3b7
2025-06-08 16:01:42.142 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-08 16:01:42.188 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-08 16:01:42.204 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.33
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-08 16:01:42.468 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-08 16:01:42.473 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:01:43.268 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-08 16:01:43.290 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 05fa91eb-d3cf-4238-a059-1d7adc44782f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-08 16:01:43.296 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-08 16:01:43.548 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-08 16:01:43.974 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-08 16:01:43.992 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-08 16:01:44.001 [main] INFO  c.k.b.BookManagementBackendApplication - Started BookManagementBackendApplication in 4.718 seconds (process running for 5.545)
2025-06-08 16:01:44.090 [main] INFO  c.k.b.config.DataInitializer - 超级管理员账户已存在，跳过初始化
2025-06-08 16:01:44.091 [main] INFO  c.k.b.config.LoggingConfig - 日志目录验证成功: D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\.\logs
2025-06-08 16:01:44.091 [main] DEBUG c.k.b.config.LoggingConfig - 这是一条DEBUG级别的测试日志
2025-06-08 16:01:44.091 [main] INFO  c.k.b.config.LoggingConfig - 这是一条INFO级别的测试日志
2025-06-08 16:01:44.091 [main] WARN  c.k.b.config.LoggingConfig - 这是一条WARN级别的测试日志
2025-06-08 16:01:44.091 [main] ERROR c.k.b.config.LoggingConfig - 这是一条ERROR级别的测试日志
2025-06-08 16:01:44.091 [main] INFO  c.k.b.config.LoggingConfig - 日志配置验证完成 - 如果您看到这条消息，说明日志系统正常工作
2025-06-08 16:03:34.525 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 16:03:34.526 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-08 16:03:34.528 [http-nio-8080-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-08 16:03:34.551 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 16:03:34.551 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 16:03:34.595 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 16:03:34.595 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 16:03:34.598 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 16:03:34.598 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 16:03:34.784 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:03:34.784 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:03:34.789 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 16:03:34.789 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 16:03:34.853 [http-nio-8080-exec-5] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 16:03:34.938 [http-nio-8080-exec-5] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 16:03:34.941 [http-nio-8080-exec-5] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 16:03:37.345 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 16:03:37.349 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 16:03:37.350 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 16:03:37.353 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:03:37.353 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 16:03:37.357 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:03:37.365 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:03:37.366 [http-nio-8080-exec-6] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 16:03:37.385 [http-nio-8080-exec-6] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 16:03:37.385 [http-nio-8080-exec-6] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 19ms, 参数: [1, 10]
2025-06-08 16:04:27.882 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:04:27.906 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:04:27.907 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:04:27.911 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:27.912 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:04:27.912 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:04:27.914 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:04:32.397 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 16:04:32.402 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 16:04:32.403 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 16:04:32.406 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:32.406 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 16:04:32.407 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:04:32.407 [http-nio-8080-exec-10] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:04:32.407 [http-nio-8080-exec-10] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 16:04:32.417 [http-nio-8080-exec-10] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 16:04:32.417 [http-nio-8080-exec-10] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 9ms, 参数: [1, 10]
2025-06-08 16:04:33.307 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 16:04:33.307 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 16:04:33.312 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 16:04:33.312 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 16:04:33.312 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 16:04:33.312 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 16:04:33.315 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:33.315 [http-nio-8080-exec-5] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:33.315 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 16:04:33.315 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 16:04:33.316 [http-nio-8080-exec-4] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 16:04:33.335 [http-nio-8080-exec-4] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 16:04:33.335 [http-nio-8080-exec-4] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 16:04:34.345 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 16:04:34.349 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 16:04:34.350 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/borrow/records, Token存在: true
2025-06-08 16:04:34.352 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:34.353 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 16:04:36.295 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:04:36.299 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:04:36.300 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:04:36.302 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:04:36.302 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:04:36.303 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:04:36.303 [http-nio-8080-exec-8] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:10:34.369 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:10:34.384 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:10:34.385 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:10:34.388 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:10:34.388 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:10:34.389 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:10:34.389 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:11:09.331 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:11:09.348 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:11:09.349 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:11:09.351 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:09.352 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:11:09.353 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:11:09.353 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:11:25.000 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 16:11:25.012 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 16:11:25.012 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 16:11:25.016 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:25.016 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 16:11:25.018 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:11:25.018 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:11:25.018 [http-nio-8080-exec-6] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 16:11:25.029 [http-nio-8080-exec-6] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 16:11:25.029 [http-nio-8080-exec-6] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 11ms, 参数: [1, 10]
2025-06-08 16:11:26.443 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 16:11:26.443 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 16:11:26.448 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 16:11:26.448 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 16:11:26.449 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 16:11:26.449 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 16:11:26.449 [http-nio-8080-exec-9] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:26.450 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 16:11:26.450 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:26.450 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 16:11:26.450 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 16:11:26.465 [http-nio-8080-exec-10] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 16:11:26.465 [http-nio-8080-exec-10] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 16:11:27.575 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 16:11:27.580 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 16:11:27.580 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/borrow/records, Token存在: true
2025-06-08 16:11:27.583 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:27.583 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
2025-06-08 16:11:29.065 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:11:29.069 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:11:29.069 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:11:29.072 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:29.072 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:11:29.073 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:11:29.073 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:11:30.233 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 16:11:30.237 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 16:11:30.238 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 16:11:30.240 [http-nio-8080-exec-6] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:11:30.242 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 16:11:30.243 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:11:30.243 [http-nio-8080-exec-6] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:11:30.243 [http-nio-8080-exec-6] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 16:11:30.249 [http-nio-8080-exec-6] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 16:11:30.250 [http-nio-8080-exec-6] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 6ms, 参数: [1, 10]
2025-06-08 16:12:26.518 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-08 16:12:26.528 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-06-08 16:12:26.544 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:12:26.545 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-08 16:12:26.552 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-08 16:12:33.455 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 16:12:33.502 [main] INFO  c.k.b.BookManagementBackendApplication - Starting BookManagementBackendApplication using Java 21.0.6 with PID 31448 (D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes started by 86187 in D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM)
2025-06-08 16:12:33.502 [main] DEBUG c.k.b.BookManagementBackendApplication - Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-08 16:12:33.503 [main] INFO  c.k.b.BookManagementBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-08 16:12:34.469 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:12:34.471 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-08 16:12:34.505 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 JPA repository interfaces.
2025-06-08 16:12:34.520 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:12:34.521 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-08 16:12:34.540 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-08 16:12:35.239 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-08 16:12:35.250 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-08 16:12:35.252 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-08 16:12:35.252 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-08 16:12:35.310 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-08 16:12:35.310 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1746 ms
2025-06-08 16:12:35.364 [main] DEBUG c.k.b.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-08 16:12:35.481 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-08 16:12:35.524 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-08 16:12:35.551 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-08 16:12:35.787 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-08 16:12:35.813 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-08 16:12:35.950 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e67d3b7
2025-06-08 16:12:35.951 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-08 16:12:36.000 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-08 16:12:36.019 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.33
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-08 16:12:36.293 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-08 16:12:36.299 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:12:36.738 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'bookController' defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\controller\BookController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'bookServiceImpl' defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BookServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'borrowRecordServiceImpl' defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BorrowRecordServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'bookServiceImpl': Requested bean is currently in creation: Is there an unresolvable circular reference or an asynchronous initialization dependency?
2025-06-08 16:12:36.739 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:12:36.741 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-08 16:12:36.748 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-08 16:12:36.749 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-08 16:12:36.759 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-08 16:12:36.773 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

The dependencies of some of the beans in the application context form a cycle:

   bookController defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\controller\BookController.class]
┌─────┐
|  bookServiceImpl defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BookServiceImpl.class]
↑     ↓
|  borrowRecordServiceImpl defined in file [D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes\com\kyx\bookmanagementbackend\service\impl\BorrowRecordServiceImpl.class]
└─────┘


Action:

Relying upon circular references is discouraged and they are prohibited by default. Update your application to remove the dependency cycle between beans. As a last resort, it may be possible to break the cycle automatically by setting spring.main.allow-circular-references to true.

2025-06-08 16:21:10.650 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-06-08 16:21:10.691 [main] INFO  c.k.b.BookManagementBackendApplication - Starting BookManagementBackendApplication using Java 21.0.6 with PID 32732 (D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\book-management-backend\target\classes started by 86187 in D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM)
2025-06-08 16:21:10.692 [main] DEBUG c.k.b.BookManagementBackendApplication - Running with Spring Boot v3.4.6, Spring v6.2.7
2025-06-08 16:21:10.693 [main] INFO  c.k.b.BookManagementBackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-08 16:21:11.620 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:21:11.621 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-08 16:21:11.653 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 JPA repository interfaces.
2025-06-08 16:21:11.667 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-08 16:21:11.668 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-08 16:21:11.688 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-08 16:21:12.338 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-08 16:21:12.353 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-08 16:21:12.355 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-08 16:21:12.355 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-06-08 16:21:12.420 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-08 16:21:12.421 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1677 ms
2025-06-08 16:21:12.481 [main] DEBUG c.k.b.s.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-06-08 16:21:12.628 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-08 16:21:12.680 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.15.Final
2025-06-08 16:21:12.714 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-08 16:21:13.004 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-08 16:21:13.039 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-08 16:21:13.214 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@70e1aa20
2025-06-08 16:21:13.218 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-08 16:21:13.274 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-08 16:21:13.291 [main] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.33
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-08 16:21:13.578 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-08 16:21:13.582 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-08 16:21:14.457 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-08 16:21:14.480 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: d881125e-b66f-4e6b-afba-2daaa8373d7d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-06-08 16:21:14.487 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-06-08 16:21:14.749 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-06-08 16:21:15.197 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-08 16:21:15.215 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-08 16:21:15.223 [main] INFO  c.k.b.BookManagementBackendApplication - Started BookManagementBackendApplication in 5.094 seconds (process running for 6.063)
2025-06-08 16:21:15.313 [main] INFO  c.k.b.config.DataInitializer - 超级管理员账户已存在，跳过初始化
2025-06-08 16:21:15.313 [main] INFO  c.k.b.config.LoggingConfig - 日志目录验证成功: D:\Code_study_project\AugmentProjes\UserCenterManagement\BookMM\.\logs
2025-06-08 16:21:15.313 [main] DEBUG c.k.b.config.LoggingConfig - 这是一条DEBUG级别的测试日志
2025-06-08 16:21:15.313 [main] INFO  c.k.b.config.LoggingConfig - 这是一条INFO级别的测试日志
2025-06-08 16:21:15.313 [main] WARN  c.k.b.config.LoggingConfig - 这是一条WARN级别的测试日志
2025-06-08 16:21:15.313 [main] ERROR c.k.b.config.LoggingConfig - 这是一条ERROR级别的测试日志
2025-06-08 16:21:15.313 [main] INFO  c.k.b.config.LoggingConfig - 日志配置验证完成 - 如果您看到这条消息，说明日志系统正常工作
2025-06-08 16:22:32.441 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-08 16:22:32.441 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-08 16:22:32.442 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-08 16:22:32.463 [http-nio-8080-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/admin/users?current=1&size=10
2025-06-08 16:22:32.517 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/admin/users?current=1&size=10
2025-06-08 16:22:32.520 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/admin/users, Token存在: true
2025-06-08 16:22:32.643 [http-nio-8080-exec-2] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:22:32.648 [http-nio-8080-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/admin/users?current=1&size=10
2025-06-08 16:22:32.716 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:22:32.722 [http-nio-8080-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.UserController.getUserList(int,int,java.lang.String,java.lang.String,java.lang.Integer); target is of class [com.kyx.bookmanagementbackend.controller.UserController]
2025-06-08 16:22:32.723 [http-nio-8080-exec-2] INFO  c.k.b.controller.UserController - 管理员查询用户列表 - 页码: 1, 大小: 10, 关键词: null, 角色: null, 状态: null, IP: 0:0:0:0:0:0:0:1
2025-06-08 16:22:32.831 [http-nio-8080-exec-2] INFO  c.k.b.utils.LogUtil - 管理员操作 - 操作: 查询用户列表, 管理员ID: null, 管理员: 管理员, 目标: 用户列表, IP: 0:0:0:0:0:0:0:1, 参数: [总数:5, 页码:1]
2025-06-08 16:22:32.831 [http-nio-8080-exec-2] DEBUG c.k.b.utils.LogUtil - 性能监控 - 操作: 查询用户列表, 耗时: 108ms, 参数: [1, 10]
2025-06-08 16:22:33.577 [http-nio-8080-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/dashboard/stats
2025-06-08 16:22:33.581 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/dashboard/stats
2025-06-08 16:22:33.582 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/dashboard/stats, Token存在: true
2025-06-08 16:22:33.585 [http-nio-8080-exec-4] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:22:33.585 [http-nio-8080-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/dashboard/stats
2025-06-08 16:22:33.586 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:22:33.586 [http-nio-8080-exec-4] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorized method invocation ReflectiveMethodInvocation: public com.kyx.bookmanagementbackend.common.Result com.kyx.bookmanagementbackend.controller.DashboardController.getDashboardStats(); target is of class [com.kyx.bookmanagementbackend.controller.DashboardController]
2025-06-08 16:22:33.586 [http-nio-8080-exec-4] INFO  c.k.b.controller.DashboardController - 开始获取仪表盘统计数据
2025-06-08 16:22:33.610 [http-nio-8080-exec-4] INFO  c.k.b.controller.DashboardController - 图书统计数据 - 总数: 8, 可借: 5, 借阅中: 0, 今日新增: 8, 本月新增: 8
2025-06-08 16:22:35.919 [http-nio-8080-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/categories
2025-06-08 16:22:35.920 [http-nio-8080-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/books?current=1&size=10
2025-06-08 16:22:35.926 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/books?current=1&size=10
2025-06-08 16:22:35.927 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/categories
2025-06-08 16:22:35.927 [http-nio-8080-exec-7] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/categories, Token存在: true
2025-06-08 16:22:35.927 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/books, Token存在: true
2025-06-08 16:22:35.930 [http-nio-8080-exec-7] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:22:35.930 [http-nio-8080-exec-8] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:22:35.931 [http-nio-8080-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/books?current=1&size=10
2025-06-08 16:22:35.932 [http-nio-8080-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/categories
2025-06-08 16:22:35.936 [http-nio-8080-exec-8] INFO  c.k.b.service.impl.BookServiceImpl - 开始查询图书列表 - 页码: 1, 大小: 10, 关键词: null, 分类ID: null, 状态: null
2025-06-08 16:22:35.970 [http-nio-8080-exec-8] INFO  c.k.b.service.impl.BookServiceImpl - 图书列表查询完成 - 总记录数: 8, 当前页记录数: 8
2025-06-08 16:22:35.971 [http-nio-8080-exec-8] INFO  c.k.b.utils.LogUtil - 业务操作 - 模块: 图书管理, 操作: 查询图书列表, 结果: 成功, 参数: [总数:8, 页码:1]
2025-06-08 16:22:38.000 [http-nio-8080-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing OPTIONS /api/borrow/records?current=1&size=10
2025-06-08 16:22:38.006 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/borrow/records?current=1&size=10
2025-06-08 16:22:38.009 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT过滤器处理请求: /api/borrow/records, Token存在: true
2025-06-08 16:22:38.011 [http-nio-8080-exec-10] DEBUG c.k.b.s.JwtAuthenticationFilter - JWT验证成功 - 用户: kyx666, 角色: SUPER_ADMIN, ID: 1
2025-06-08 16:22:38.012 [http-nio-8080-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/borrow/records?current=1&size=10
