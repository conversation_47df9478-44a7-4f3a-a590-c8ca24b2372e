<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #667eea;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔖 收藏功能API测试</h1>
        
        <!-- 登录区域 -->
        <div class="test-section">
            <h3>1. 用户登录</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="123456" placeholder="输入密码">
            </div>
            <button onclick="login()">登录</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- 收藏操作区域 -->
        <div class="test-section">
            <h3>2. 收藏操作测试</h3>
            <div class="form-group">
                <label>图书ID:</label>
                <input type="number" id="bookId" value="1" placeholder="输入图书ID">
            </div>
            <button onclick="toggleFavorite()">切换收藏状态</button>
            <button onclick="checkFavoriteStatus()">检查收藏状态</button>
            <button onclick="addFavorite()">添加收藏</button>
            <button onclick="removeFavorite()">取消收藏</button>
            <div id="favoriteResult" class="result" style="display: none;"></div>
        </div>

        <!-- 收藏列表区域 -->
        <div class="test-section">
            <h3>3. 收藏列表测试</h3>
            <div class="form-group">
                <label>页码:</label>
                <input type="number" id="currentPage" value="1" placeholder="页码">
            </div>
            <div class="form-group">
                <label>每页大小:</label>
                <input type="number" id="pageSize" value="10" placeholder="每页大小">
            </div>
            <button onclick="getUserFavoriteList()">获取收藏列表</button>
            <button onclick="getUserFavoriteBookIds()">获取收藏图书ID列表</button>
            <button onclick="getUserFavoriteStats()">获取收藏统计</button>
            <div id="listResult" class="result" style="display: none;"></div>
        </div>

        <!-- 批量操作区域 -->
        <div class="test-section">
            <h3>4. 批量操作测试</h3>
            <div class="form-group">
                <label>图书ID列表 (逗号分隔):</label>
                <input type="text" id="bookIds" value="1,2,3" placeholder="例如: 1,2,3">
            </div>
            <button onclick="batchCheckFavoriteStatus()">批量检查收藏状态</button>
            <div id="batchResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        let authToken = '';

        // 显示结果
        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 登录
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (data.code === 200 && data.data && data.data.token) {
                    authToken = data.data.token;
                    showResult('loginResult', { 
                        message: '登录成功', 
                        token: authToken.substring(0, 20) + '...',
                        user: data.data.user 
                    }, 'success');
                } else {
                    showResult('loginResult', data, 'error');
                }
            } catch (error) {
                showResult('loginResult', { error: error.message }, 'error');
            }
        }

        // 通用API调用
        async function callAPI(url, method = 'GET', body = null) {
            if (!authToken) {
                return { error: '请先登录获取token' };
            }

            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                };

                if (body) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(url, options);
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        }

        // 切换收藏状态
        async function toggleFavorite() {
            const bookId = parseInt(document.getElementById('bookId').value);
            const result = await callAPI(`${API_BASE}/user/favorites/toggle`, 'POST', { bookId });
            showResult('favoriteResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 检查收藏状态
        async function checkFavoriteStatus() {
            const bookId = parseInt(document.getElementById('bookId').value);
            const result = await callAPI(`${API_BASE}/user/favorites/check/${bookId}`);
            showResult('favoriteResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 添加收藏
        async function addFavorite() {
            const bookId = parseInt(document.getElementById('bookId').value);
            const result = await callAPI(`${API_BASE}/user/favorites/add`, 'POST', { bookId });
            showResult('favoriteResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 取消收藏
        async function removeFavorite() {
            const bookId = parseInt(document.getElementById('bookId').value);
            const result = await callAPI(`${API_BASE}/user/favorites/remove`, 'POST', { bookId });
            showResult('favoriteResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 获取收藏列表
        async function getUserFavoriteList() {
            const current = parseInt(document.getElementById('currentPage').value);
            const size = parseInt(document.getElementById('pageSize').value);
            const result = await callAPI(`${API_BASE}/user/favorites/list?current=${current}&size=${size}`);
            showResult('listResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 获取收藏图书ID列表
        async function getUserFavoriteBookIds() {
            const result = await callAPI(`${API_BASE}/user/favorites/book-ids`);
            showResult('listResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 获取收藏统计
        async function getUserFavoriteStats() {
            const result = await callAPI(`${API_BASE}/user/favorites/stats`);
            showResult('listResult', result, result.code === 200 ? 'success' : 'error');
        }

        // 批量检查收藏状态
        async function batchCheckFavoriteStatus() {
            const bookIdsStr = document.getElementById('bookIds').value;
            const bookIds = bookIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            const result = await callAPI(`${API_BASE}/user/favorites/batch-check`, 'POST', bookIds);
            showResult('batchResult', result, result.code === 200 ? 'success' : 'error');
        }
    </script>
</body>
</html>
