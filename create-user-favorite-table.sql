-- 创建用户收藏表
CREATE TABLE IF NOT EXISTS `user_favorite` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `book_id` BIGINT NOT NULL COMMENT '图书ID',
    `favorite_time` DATETIME NOT NULL COMMENT '收藏时间',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除（0-未删除，1-已删除）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_book` (`user_id`, `book_id`, `deleted`) COMMENT '用户图书唯一索引',
    KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
    KEY `idx_book_id` (`book_id`) COMMENT '图书ID索引',
    KEY `idx_favorite_time` (`favorite_time`) COMMENT '收藏时间索引',
    KEY `idx_created_time` (`created_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 添加外键约束（可选，根据实际情况决定是否添加）
-- ALTER TABLE `user_favorite` ADD CONSTRAINT `fk_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `user_favorite` ADD CONSTRAINT `fk_favorite_book` FOREIGN KEY (`book_id`) REFERENCES `book` (`id`) ON DELETE CASCADE;

-- 插入测试数据（可选）
INSERT INTO `user_favorite` (`user_id`, `book_id`, `favorite_time`, `created_time`, `updated_time`, `deleted`) VALUES
(1, 1, '2024-01-15 10:30:00', NOW(), NOW(), 0),
(1, 2, '2024-01-16 14:20:00', NOW(), NOW(), 0),
(1, 3, '2024-01-17 09:15:00', NOW(), NOW(), 0),
(2, 1, '2024-01-18 16:45:00', NOW(), NOW(), 0),
(2, 4, '2024-01-19 11:30:00', NOW(), NOW(), 0);

-- 查询验证
SELECT 
    uf.id,
    uf.user_id,
    u.username,
    uf.book_id,
    b.title as book_title,
    uf.favorite_time,
    uf.created_time
FROM user_favorite uf
LEFT JOIN sys_user u ON uf.user_id = u.id
LEFT JOIN book b ON uf.book_id = b.id
WHERE uf.deleted = 0
ORDER BY uf.created_time DESC;
