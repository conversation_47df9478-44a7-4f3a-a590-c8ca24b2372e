-- 测试时间字段修复的SQL脚本

-- 检查当前借阅记录表的数据
SELECT 
    '=== 当前借阅记录数据 ===' as info;

SELECT 
    br.id,
    br.user_id,
    u.username,
    br.book_id,
    b.title as book_title,
    br.borrow_time,
    br.due_time,
    br.return_time,
    br.status,
    br.created_time,
    br.updated_time
FROM borrow_record br
LEFT JOIN sys_user u ON br.user_id = u.id
LEFT JOIN book b ON br.book_id = b.id
WHERE br.deleted = 0
ORDER BY br.created_time DESC
LIMIT 10;

-- 如果没有数据，插入一些测试数据
INSERT IGNORE INTO borrow_record (
    user_id, 
    book_id, 
    borrow_time, 
    due_time, 
    return_time,
    status, 
    overdue_days, 
    fine_amount, 
    remarks,
    created_time,
    updated_time,
    deleted
) VALUES 
-- 当前借阅记录
(1, 1, '2025-06-01 10:30:00', '2025-07-01 10:30:00', NULL, 'BORROWED', 0, 0.00, '测试当前借阅1', NOW(), NOW(), 0),
(1, 2, '2025-06-05 14:15:00', '2025-07-05 14:15:00', NULL, 'BORROWED', 0, 0.00, '测试当前借阅2', NOW(), NOW(), 0),

-- 历史借阅记录（已归还）
(1, 3, '2025-05-01 09:20:00', '2025-05-31 09:20:00', '2025-05-25 16:45:00', 'RETURNED', 0, 0.00, '测试历史记录1', NOW(), NOW(), 0),
(1, 4, '2025-04-15 11:30:00', '2025-05-15 11:30:00', '2025-05-10 10:15:00', 'RETURNED', 0, 0.00, '测试历史记录2', NOW(), NOW(), 0),

-- 逾期记录
(1, 5, '2025-04-01 13:45:00', '2025-05-01 13:45:00', '2025-05-05 15:30:00', 'RETURNED', 4, 4.00, '测试逾期记录', NOW(), NOW(), 0);

-- 验证插入的数据
SELECT 
    '=== 插入后的借阅记录数据 ===' as info;

SELECT 
    br.id,
    br.user_id,
    u.username,
    br.book_id,
    b.title as book_title,
    br.borrow_time,
    br.due_time,
    br.return_time,
    br.status,
    br.remarks,
    br.created_time
FROM borrow_record br
LEFT JOIN sys_user u ON br.user_id = u.id
LEFT JOIN book b ON br.book_id = b.id
WHERE br.deleted = 0 AND br.user_id = 1
ORDER BY br.created_time DESC;
