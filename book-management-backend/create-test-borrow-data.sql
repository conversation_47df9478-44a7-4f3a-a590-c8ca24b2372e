-- 创建测试借阅记录数据

-- 首先检查是否有用户和图书数据
SELECT 'Checking users...' as info;
SELECT id, username, role FROM sys_user WHERE deleted = 0 LIMIT 5;

SELECT 'Checking books...' as info;
SELECT id, title, author, available_quantity FROM book WHERE deleted = 0 AND status = 1 LIMIT 5;

-- 为用户ID=1创建一些测试借阅记录（假设存在）
-- 注意：请根据实际的用户ID和图书ID调整

-- 当前借阅记录
INSERT INTO borrow_record (
    user_id, 
    book_id, 
    borrow_time, 
    due_time, 
    status, 
    overdue_days, 
    fine_amount, 
    remarks,
    created_time,
    updated_time
) VALUES 
-- 假设用户ID=1，图书ID=1,2,3存在
(1, 1, '2025-06-01 10:00:00', '2025-07-01 10:00:00', 'BORROWED', 0, 0.00, '测试借阅记录1', NOW(), NOW()),
(1, 2, '2025-06-05 14:30:00', '2025-07-05 14:30:00', 'BORROWED', 0, 0.00, '测试借阅记录2', NOW(), NOW());

-- 历史借阅记录（已归还）
INSERT INTO borrow_record (
    user_id, 
    book_id, 
    borrow_time, 
    due_time, 
    return_time,
    status, 
    overdue_days, 
    fine_amount, 
    remarks,
    created_time,
    updated_time
) VALUES 
(1, 3, '2025-05-01 09:00:00', '2025-05-31 09:00:00', '2025-05-25 16:00:00', 'RETURNED', 0, 0.00, '测试历史记录1', NOW(), NOW()),
(1, 4, '2025-04-15 11:00:00', '2025-05-15 11:00:00', '2025-05-10 10:00:00', 'RETURNED', 0, 0.00, '测试历史记录2', NOW(), NOW()),
(1, 5, '2025-04-01 13:00:00', '2025-05-01 13:00:00', '2025-05-05 15:00:00', 'RETURNED', 4, 4.00, '测试逾期记录', NOW(), NOW());

-- 验证插入的数据
SELECT 'Inserted borrow records:' as info;
SELECT 
    br.id,
    br.user_id,
    br.book_id,
    br.borrow_time,
    br.due_time,
    br.return_time,
    br.status,
    br.remarks
FROM borrow_record br
WHERE br.user_id = 1 AND br.deleted = 0
ORDER BY br.created_time DESC;
