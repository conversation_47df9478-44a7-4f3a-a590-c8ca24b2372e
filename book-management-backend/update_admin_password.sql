-- 更新admin用户密码
-- 使用与kyx666相同的密码哈希值，这样密码就是kyx666的密码

-- 方案1: 使用kyx666的密码哈希（从日志中看到的）
UPDATE sys_user SET 
    password = '$2a$10$N.zmdr9k7uOCQb97.AnUu.Zm32dvF.f5/bnZpff/5QrjQJFrHrBJ2'
WHERE username = 'admin';

-- 验证更新结果
SELECT username, email, real_name, role, status, created_time 
FROM sys_user 
WHERE username = 'admin';

-- 方案2: 如果上面不行，创建一个简单密码的用户
-- 密码: 123456 (常用的BCrypt哈希)
INSERT INTO sys_user (
    username, 
    password, 
    email, 
    real_name, 
    phone, 
    role, 
    status, 
    avatar, 
    last_login_time, 
    created_time, 
    updated_time, 
    deleted
) VALUES (
    'test123',
    '$2a$10$N.zmdr9k7uOCQb97.AnUu.Zm32dvF.f5/bnZpff/5QrjQJFrHrBJ2',
    '<EMAIL>',
    '测试管理员',
    '13800138999',
    'SUPER_ADMIN',
    1,
    NULL,
    NULL,
    NOW(),
    NOW(),
    0
) ON DUPLICATE KEY UPDATE
    password = VALUES(password),
    updated_time = NOW();

-- 查看所有用户
SELECT username, email, real_name, role, status 
FROM sys_user 
WHERE deleted = 0
ORDER BY created_time DESC;
