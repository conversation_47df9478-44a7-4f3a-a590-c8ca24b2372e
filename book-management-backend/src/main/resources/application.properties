# ????
spring.application.name=book-management-backend
server.port=8080

# ?????
spring.datasource.url=*********************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=kyx200328
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA??
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# MyBatis Plus??
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml

# Redis??
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=10000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# JWT??
jwt.secret=bookManagementSecretKeyForJWTTokenGenerationAndValidation2024
jwt.expiration=86400000

# ??????
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ???? - ??logback-spring.xml????
# ??????logback-spring.xml???????????????
logging.config=classpath:logback-spring.xml

# Swagger??
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
