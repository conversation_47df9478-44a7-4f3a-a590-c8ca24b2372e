package com.kyx.bookmanagementbackend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.dto.UserLoginDTO;
import com.kyx.bookmanagementbackend.dto.UserRegisterDTO;
import com.kyx.bookmanagementbackend.entity.User;
import com.kyx.bookmanagementbackend.exception.BusinessException;
import com.kyx.bookmanagementbackend.mapper.UserMapper;
import com.kyx.bookmanagementbackend.service.UserService;
import com.kyx.bookmanagementbackend.utils.JwtUtil;
import com.kyx.bookmanagementbackend.vo.LoginVO;
import com.kyx.bookmanagementbackend.vo.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import com.kyx.bookmanagementbackend.utils.LogUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    
    @Override
    public LoginVO login(UserLoginDTO loginDTO) {
        log.info("用户登录验证开始 - 用户名: {}", loginDTO.getUsername());

        // 查询用户
        User user = findByUsername(loginDTO.getUsername());
        if (user == null) {
            log.warn("用户登录失败 - 用户不存在: {}", loginDTO.getUsername());
            LogUtil.logSecurityEvent("登录失败", loginDTO.getUsername(), "用户不存在");
            throw new BusinessException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            log.warn("用户登录失败 - 用户已被禁用: {}, 用户ID: {}", loginDTO.getUsername(), user.getId());
            LogUtil.logSecurityEvent("登录失败", loginDTO.getUsername(), "用户已被禁用");
            throw new BusinessException("用户已被禁用");
        }

        // 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            log.warn("用户登录失败 - 密码错误: {}, 用户ID: {}", loginDTO.getUsername(), user.getId());
            LogUtil.logSecurityEvent("登录失败", loginDTO.getUsername(), "密码错误");
            throw new BusinessException("密码错误");
        }

        // 更新最后登录时间
        updateLastLoginTime(user.getId());

        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername(), user.getRole(), user.getId());

        // 构建用户信息
        UserVO userVO = BeanUtil.copyProperties(user, UserVO.class);

        log.info("用户登录成功 - 用户名: {}, 用户ID: {}, 角色: {}",
                loginDTO.getUsername(), user.getId(), user.getRole());
        LogUtil.logUserOperation("用户登录", user.getId(), user.getUsername(), "角色:" + user.getRole());

        return new LoginVO(token, userVO);
    }
    
    @Override
    public void register(UserRegisterDTO registerDTO) {
        // 验证密码确认
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 检查用户名是否已存在
        if (findByUsername(registerDTO.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (findByEmail(registerDTO.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 创建用户
        User user = new User();
        BeanUtil.copyProperties(registerDTO, user);
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setRole("USER");
        user.setStatus(1);
        
        userMapper.insert(user);
    }
    
    @Override
    public User findByUsername(String username) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        return userMapper.selectOne(wrapper);
    }
    
    @Override
    public User findByEmail(String email) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getEmail, email);
        return userMapper.selectOne(wrapper);
    }
    
    @Override
    public Page<UserVO> getUserList(int current, int size, String keyword, String role, Integer status) {
        Page<User> page = new Page<>(current, size);
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        
        // 关键词搜索
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.and(w -> w.like(User::getUsername, keyword)
                    .or().like(User::getRealName, keyword)
                    .or().like(User::getEmail, keyword));
        }
        
        // 角色筛选
        if (StrUtil.isNotBlank(role)) {
            wrapper.eq(User::getRole, role);
        }
        
        // 状态筛选
        if (status != null) {
            wrapper.eq(User::getStatus, status);
        }
        
        wrapper.orderByDesc(User::getCreatedTime);
        
        Page<User> userPage = userMapper.selectPage(page, wrapper);
        
        // 转换为VO
        Page<UserVO> voPage = new Page<>();
        BeanUtil.copyProperties(userPage, voPage);
        List<UserVO> userVOList = userPage.getRecords().stream()
                .map(user -> BeanUtil.copyProperties(user, UserVO.class))
                .toList();
        voPage.setRecords(userVOList);
        
        return voPage;
    }
    
    @Override
    public UserVO getUserById(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return BeanUtil.copyProperties(user, UserVO.class);
    }
    
    @Override
    public void createUser(UserRegisterDTO userDTO, String role) {
        // 检查用户名是否已存在
        if (findByUsername(userDTO.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (findByEmail(userDTO.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 创建用户
        User user = new User();
        BeanUtil.copyProperties(userDTO, user);
        user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
        user.setRole(role);
        user.setStatus(1);
        
        userMapper.insert(user);
    }
    
    @Override
    public void updateUser(Long id, UserVO userVO) {
        User existingUser = userMapper.selectById(id);
        if (existingUser == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查邮箱是否被其他用户使用
        if (!existingUser.getEmail().equals(userVO.getEmail())) {
            User emailUser = findByEmail(userVO.getEmail());
            if (emailUser != null && !emailUser.getId().equals(id)) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
        }
        
        // 更新用户信息
        User user = new User();
        BeanUtil.copyProperties(userVO, user);
        user.setId(id);
        userMapper.updateById(user);
    }
    
    @Override
    public void deleteUser(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 不能删除超级管理员
        if ("SUPER_ADMIN".equals(user.getRole())) {
            throw new BusinessException("不能删除超级管理员");
        }
        
        userMapper.deleteById(id);
    }
    
    @Override
    public void toggleUserStatus(Long id) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 不能禁用超级管理员
        if ("SUPER_ADMIN".equals(user.getRole())) {
            throw new BusinessException("不能禁用超级管理员");
        }
        
        user.setStatus(user.getStatus() == 1 ? 0 : 1);
        userMapper.updateById(user);
    }
    
    @Override
    public void resetPassword(Long id, String newPassword) {
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        user.setPassword(passwordEncoder.encode(newPassword));
        userMapper.updateById(user);
    }
    
    @Override
    public void updateLastLoginTime(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    @Override
    public long getTotalUserCount() {
        return userMapper.selectCount(null);
    }

    @Override
    public long getActiveUserCount() {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getStatus, 1);
        return userMapper.selectCount(wrapper);
    }

    @Override
    public long getNewUsersToday() {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.ge(User::getCreatedTime, startOfDay);
        return userMapper.selectCount(wrapper);
    }

    @Override
    public long getNewUsersThisMonth() {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        wrapper.ge(User::getCreatedTime, startOfMonth);
        return userMapper.selectCount(wrapper);
    }
}
