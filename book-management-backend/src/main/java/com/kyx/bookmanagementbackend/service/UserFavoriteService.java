package com.kyx.bookmanagementbackend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kyx.bookmanagementbackend.entity.UserFavorite;
import com.kyx.bookmanagementbackend.vo.UserFavoriteVO;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏服务接口
 */
public interface UserFavoriteService extends IService<UserFavorite> {

    /**
     * 添加收藏
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 是否成功
     */
    boolean addFavorite(Long userId, Long bookId);

    /**
     * 取消收藏
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 是否成功
     */
    boolean removeFavorite(Long userId, Long bookId);

    /**
     * 切换收藏状态
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 操作结果：true-已收藏，false-已取消收藏
     */
    boolean toggleFavorite(Long userId, Long bookId);

    /**
     * 检查用户是否收藏了指定图书
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 是否已收藏
     */
    boolean isFavorited(Long userId, Long bookId);

    /**
     * 分页查询用户收藏列表
     *
     * @param userId  用户ID
     * @param current 当前页
     * @param size    每页大小
     * @return 收藏列表
     */
    IPage<UserFavoriteVO> getUserFavorites(Long userId, Long current, Long size);

    /**
     * 获取用户收藏的图书ID列表
     *
     * @param userId 用户ID
     * @return 图书ID列表
     */
    List<Long> getUserFavoriteBookIds(Long userId);

    /**
     * 获取图书的收藏数量
     *
     * @param bookId 图书ID
     * @return 收藏数量
     */
    int getBookFavoriteCount(Long bookId);

    /**
     * 批量查询图书收藏状态
     *
     * @param userId  用户ID
     * @param bookIds 图书ID列表
     * @return 图书ID -> 是否收藏的映射
     */
    Map<Long, Boolean> batchCheckFavoriteStatus(Long userId, List<Long> bookIds);

    /**
     * 获取用户收藏统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserFavoriteStats(Long userId);
}
