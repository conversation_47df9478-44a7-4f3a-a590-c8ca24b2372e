package com.kyx.bookmanagementbackend.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户收藏视图对象
 */
@Data
public class UserFavoriteVO {

    /**
     * 收藏ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户真实姓名
     */
    private String userRealName;

    /**
     * 图书ID
     */
    private Long bookId;

    /**
     * 图书标题
     */
    private String bookTitle;

    /**
     * 图书作者
     */
    private String bookAuthor;

    /**
     * 图书ISBN
     */
    private String bookIsbn;

    /**
     * 图书分类
     */
    private String bookCategory;

    /**
     * 图书封面URL
     */
    private String bookCoverUrl;

    /**
     * 图书状态
     */
    private Integer bookStatus;

    /**
     * 图书可借数量
     */
    private Integer bookAvailableQuantity;

    /**
     * 收藏时间
     */
    private LocalDateTime favoriteTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
