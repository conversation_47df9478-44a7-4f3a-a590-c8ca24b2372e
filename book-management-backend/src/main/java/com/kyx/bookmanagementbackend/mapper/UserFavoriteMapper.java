package com.kyx.bookmanagementbackend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.entity.UserFavorite;
import com.kyx.bookmanagementbackend.vo.UserFavoriteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户收藏Mapper接口
 */
@Mapper
public interface UserFavoriteMapper extends BaseMapper<UserFavorite> {

    /**
     * 分页查询用户收藏列表（带图书信息）
     *
     * @param page   分页对象
     * @param userId 用户ID
     * @return 收藏列表
     */
    IPage<UserFavoriteVO> selectUserFavoritesWithBookInfo(Page<UserFavoriteVO> page, @Param("userId") Long userId);

    /**
     * 查询用户收藏的图书ID列表
     *
     * @param userId 用户ID
     * @return 图书ID列表
     */
    List<Long> selectUserFavoriteBookIds(@Param("userId") Long userId);

    /**
     * 检查用户是否收藏了指定图书
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 收藏记录数量
     */
    int checkUserFavorite(@Param("userId") Long userId, @Param("bookId") Long bookId);

    /**
     * 获取图书的收藏数量
     *
     * @param bookId 图书ID
     * @return 收藏数量
     */
    int getBookFavoriteCount(@Param("bookId") Long bookId);

    /**
     * 批量查询图书收藏状态
     *
     * @param userId  用户ID
     * @param bookIds 图书ID列表
     * @return 已收藏的图书ID列表
     */
    List<Long> batchCheckFavoriteStatus(@Param("userId") Long userId, @Param("bookIds") List<Long> bookIds);
}
