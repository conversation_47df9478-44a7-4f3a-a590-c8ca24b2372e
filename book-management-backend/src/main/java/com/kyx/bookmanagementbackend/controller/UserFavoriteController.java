package com.kyx.bookmanagementbackend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.FavoriteDTO;
import com.kyx.bookmanagementbackend.service.UserFavoriteService;
import com.kyx.bookmanagementbackend.vo.UserFavoriteVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 用户收藏控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user/favorites")
public class UserFavoriteController {

    @Autowired
    private UserFavoriteService userFavoriteService;

    /**
     * 添加收藏
     */
    @PostMapping("/add")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> addFavorite(@Valid @RequestBody FavoriteDTO favoriteDTO, Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求添加收藏图书{}", userId, favoriteDTO.getBookId());

            userFavoriteService.addFavorite(userId, favoriteDTO.getBookId());
            return Result.success("收藏成功");
        } catch (Exception e) {
            log.error("添加收藏失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消收藏
     */
    @PostMapping("/remove")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<String> removeFavorite(@Valid @RequestBody FavoriteDTO favoriteDTO, Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求取消收藏图书{}", userId, favoriteDTO.getBookId());

            userFavoriteService.removeFavorite(userId, favoriteDTO.getBookId());
            return Result.success("取消收藏成功");
        } catch (Exception e) {
            log.error("取消收藏失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 切换收藏状态
     */
    @PostMapping("/toggle")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> toggleFavorite(@Valid @RequestBody FavoriteDTO favoriteDTO, Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}请求切换收藏状态，图书{}", userId, favoriteDTO.getBookId());

            boolean isFavorited = userFavoriteService.toggleFavorite(userId, favoriteDTO.getBookId());

            Map<String, Object> result = Map.of(
                "isFavorited", isFavorited,
                "message", isFavorited ? "收藏成功" : "取消收藏成功"
            );

            return Result.success(result);
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查收藏状态
     */
    @GetMapping("/check/{bookId}")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Boolean> checkFavorite(@PathVariable Long bookId, Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}检查图书{}收藏状态", userId, bookId);

            boolean isFavorited = userFavoriteService.isFavorited(userId, bookId);
            return Result.success(isFavorited);
        } catch (Exception e) {
            log.error("检查收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页查询用户收藏列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<IPage<UserFavoriteVO>> getUserFavorites(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}查询收藏列表，页码{}，大小{}", userId, current, size);

            IPage<UserFavoriteVO> favorites = userFavoriteService.getUserFavorites(userId, current, size);
            return Result.success(favorites);
        } catch (Exception e) {
            log.error("查询收藏列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户收藏的图书ID列表
     */
    @GetMapping("/book-ids")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<List<Long>> getUserFavoriteBookIds(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}查询收藏图书ID列表", userId);

            List<Long> bookIds = userFavoriteService.getUserFavoriteBookIds(userId);
            return Result.success(bookIds);
        } catch (Exception e) {
            log.error("查询收藏图书ID列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量查询图书收藏状态
     */
    @PostMapping("/batch-check")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<Long, Boolean>> batchCheckFavoriteStatus(@RequestBody List<Long> bookIds, Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.debug("用户{}批量查询图书收藏状态", userId);

            Map<Long, Boolean> favoriteStatus = userFavoriteService.batchCheckFavoriteStatus(userId, bookIds);
            return Result.success(favoriteStatus);
        } catch (Exception e) {
            log.error("批量查询收藏状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户收藏统计信息
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public Result<Map<String, Object>> getUserFavoriteStats(Authentication authentication) {
        try {
            Long userId = (Long) authentication.getDetails();
            log.info("用户{}查询收藏统计信息", userId);

            Map<String, Object> stats = userFavoriteService.getUserFavoriteStats(userId);
            return Result.success(stats);
        } catch (Exception e) {
            log.error("查询收藏统计信息失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取图书收藏数量
     */
    @GetMapping("/book/{bookId}/count")
    public Result<Integer> getBookFavoriteCount(@PathVariable Long bookId) {
        try {
            log.debug("查询图书{}的收藏数量", bookId);

            int count = userFavoriteService.getBookFavoriteCount(bookId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("查询图书收藏数量失败", e);
            return Result.error(e.getMessage());
        }
    }
}
