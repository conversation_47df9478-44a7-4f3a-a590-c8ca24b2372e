package com.kyx.bookmanagementbackend.controller;

import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.service.BookService;
import com.kyx.bookmanagementbackend.service.BorrowRecordService;
import com.kyx.bookmanagementbackend.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 仪表盘控制器
 */
@Tag(name = "仪表盘", description = "仪表盘统计数据相关接口")
@Slf4j
@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
public class DashboardController {
    
    private final UserService userService;
    private final BookService bookService;
    private final BorrowRecordService borrowRecordService;
    
    @Operation(summary = "获取仪表盘统计数据")
    @GetMapping("/stats")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<Map<String, Object>> getDashboardStats() {
        log.info("开始获取仪表盘统计数据");
        Map<String, Object> stats = new HashMap<>();
        
        // 用户统计
        Map<String, Object> userStats = new HashMap<>();
        userStats.put("totalUsers", userService.getTotalUserCount());
        userStats.put("activeUsers", userService.getActiveUserCount());
        userStats.put("newUsersToday", userService.getNewUsersToday());
        userStats.put("newUsersThisMonth", userService.getNewUsersThisMonth());
        stats.put("userStats", userStats);
        
        // 图书统计
        Map<String, Object> bookStats = new HashMap<>();
        long totalBooks = bookService.getTotalBookCount();
        long availableBooks = bookService.getAvailableBookCount();
        long borrowedBooks = borrowRecordService.getActiveBorrowCount(); // 直接从借阅记录服务获取
        long newBooksToday = bookService.getNewBooksToday();
        long newBooksThisMonth = bookService.getNewBooksThisMonth();

        log.info("图书统计数据 - 总数: {}, 可借: {}, 借阅中: {}, 今日新增: {}, 本月新增: {}",
                totalBooks, availableBooks, borrowedBooks, newBooksToday, newBooksThisMonth);

        bookStats.put("totalBooks", totalBooks);
        bookStats.put("availableBooks", availableBooks);
        bookStats.put("borrowedBooks", borrowedBooks);
        bookStats.put("newBooksToday", newBooksToday);
        bookStats.put("newBooksThisMonth", newBooksThisMonth);
        stats.put("bookStats", bookStats);
        
        // 借阅统计
        Map<String, Object> borrowStats = new HashMap<>();
        borrowStats.put("totalBorrows", borrowRecordService.getTotalBorrowCount());
        borrowStats.put("activeBorrows", borrowRecordService.getActiveBorrowCount());
        borrowStats.put("overdueCount", borrowRecordService.getOverdueCount());
        borrowStats.put("returnedToday", borrowRecordService.getReturnedToday());
        borrowStats.put("borrowedToday", borrowRecordService.getBorrowedToday());
        stats.put("borrowStats", borrowStats);
        
        // 系统统计
        Map<String, Object> systemStats = new HashMap<>();
        systemStats.put("totalCategories", 12); // 暂时使用固定值
        systemStats.put("totalFines", 1250.50); // 暂时使用固定值
        systemStats.put("systemUptime", "15天 8小时 32分钟"); // 暂时使用固定值
        systemStats.put("lastBackupTime", "2025-06-08 02:00:00"); // 暂时使用固定值
        stats.put("systemStats", systemStats);
        
        return Result.success(stats);
    }
    
    @Operation(summary = "获取系统健康状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("database", "HEALTHY");
        health.put("redis", "HEALTHY");
        health.put("storage", "HEALTHY");
        
        Map<String, Object> memory = new HashMap<>();
        memory.put("used", 512);
        memory.put("total", 1024);
        memory.put("percentage", 50);
        health.put("memory", memory);
        
        Map<String, Object> disk = new HashMap<>();
        disk.put("used", 25);
        disk.put("total", 100);
        disk.put("percentage", 25);
        health.put("disk", disk);
        
        return Result.success(health);
    }
}
