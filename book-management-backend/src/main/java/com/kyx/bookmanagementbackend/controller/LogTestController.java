package com.kyx.bookmanagementbackend.controller;

import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.service.BorrowRecordService;
import com.kyx.bookmanagementbackend.service.BookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志测试控制器
 * 用于测试日志文件生成
 */
@Tag(name = "日志测试", description = "用于测试日志文件生成的接口")
@Slf4j
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class LogTestController {

    private final BorrowRecordService borrowRecordService;
    private final BookService bookService;

    @Operation(summary = "测试日志输出")
    @GetMapping("/log")
    public Result<String> testLog(@RequestParam(defaultValue = "INFO") String level) {
        
        switch (level.toUpperCase()) {
            case "DEBUG":
                log.debug("这是一条DEBUG级别的测试日志 - 时间: {}", System.currentTimeMillis());
                break;
            case "INFO":
                log.info("这是一条INFO级别的测试日志 - 时间: {}", System.currentTimeMillis());
                break;
            case "WARN":
                log.warn("这是一条WARN级别的测试日志 - 时间: {}", System.currentTimeMillis());
                break;
            case "ERROR":
                log.error("这是一条ERROR级别的测试日志 - 时间: {}", System.currentTimeMillis());
                break;
            default:
                log.info("默认INFO级别测试日志 - 时间: {}", System.currentTimeMillis());
        }
        
        return Result.success("日志测试完成，级别: " + level);
    }

    @Operation(summary = "生成大量日志用于测试文件轮转")
    @GetMapping("/bulk-log")
    public Result<String> bulkLog(@RequestParam(defaultValue = "100") int count) {
        
        for (int i = 1; i <= count; i++) {
            if (i % 10 == 0) {
                log.error("批量日志测试 - ERROR级别 - 第{}条", i);
            } else if (i % 5 == 0) {
                log.warn("批量日志测试 - WARN级别 - 第{}条", i);
            } else {
                log.info("批量日志测试 - INFO级别 - 第{}条", i);
            }
        }
        
        return Result.success("生成了 " + count + " 条测试日志");
    }

    @Operation(summary = "检查借阅统计数据")
    @GetMapping("/borrow-stats")
    public Result<Map<String, Object>> checkBorrowStats() {
        Map<String, Object> stats = new HashMap<>();

        // 获取借阅统计数据
        long activeBorrowCount = borrowRecordService.getActiveBorrowCount();
        long totalBorrowCount = borrowRecordService.getTotalBorrowCount();
        long overdueCount = borrowRecordService.getOverdueCount();

        stats.put("activeBorrowCount", activeBorrowCount);
        stats.put("totalBorrowCount", totalBorrowCount);
        stats.put("overdueCount", overdueCount);
        stats.put("returnedCount", totalBorrowCount - activeBorrowCount);

        log.info("借阅统计检查 - 活跃借阅数: {}, 总借阅数: {}, 逾期数: {}",
                activeBorrowCount, totalBorrowCount, overdueCount);

        return Result.success(stats);
    }
}
