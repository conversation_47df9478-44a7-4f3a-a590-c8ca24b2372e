package com.kyx.bookmanagementbackend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kyx.bookmanagementbackend.common.Result;
import com.kyx.bookmanagementbackend.dto.UserRegisterDTO;
import com.kyx.bookmanagementbackend.service.UserService;
import com.kyx.bookmanagementbackend.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.kyx.bookmanagementbackend.utils.LogUtil;

/**
 * 用户管理控制器
 */
@Tag(name = "用户管理", description = "用户管理相关接口")
@Slf4j
@RestController
@RequestMapping("/api/admin/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    @Operation(summary = "分页查询用户列表")
    @GetMapping
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<Page<UserVO>> getUserList(
            @Parameter(description = "当前页") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "角色筛选") @RequestParam(required = false) String role,
            @Parameter(description = "状态筛选") @RequestParam(required = false) Integer status) {

        long startTime = System.currentTimeMillis();
        log.info("管理员查询用户列表 - 页码: {}, 大小: {}, 关键词: {}, 角色: {}, 状态: {}, IP: {}",
                current, size, keyword, role, status, LogUtil.getClientIp());

        try {
            Page<UserVO> userPage = userService.getUserList(current, size, keyword, role, status);
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logAdminOperation("查询用户列表", null, "管理员",
                    "用户列表", "总数:" + userPage.getTotal(), "页码:" + current);
            LogUtil.logPerformance("查询用户列表", duration, current, size);
            return Result.success(userPage);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logSystemError("用户管理", "查询用户列表", e.getMessage(), e);
            throw e;
        }
    }
    
    @Operation(summary = "根据ID查询用户详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ADMIN')")
    public Result<UserVO> getUserById(@PathVariable Long id) {
        UserVO userVO = userService.getUserById(id);
        return Result.success(userVO);
    }
    
    @Operation(summary = "创建用户")
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<String> createUser(@Validated @RequestBody UserRegisterDTO userDTO,
                                  @Parameter(description = "用户角色") @RequestParam String role) {
        long startTime = System.currentTimeMillis();
        log.info("管理员创建用户 - 用户名: {}, 邮箱: {}, 角色: {}, IP: {}",
                userDTO.getUsername(), userDTO.getEmail(), role, LogUtil.getClientIp());

        try {
            userService.createUser(userDTO, role);
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logAdminOperation("创建用户", null, "超级管理员",
                    "用户:" + userDTO.getUsername(), "角色:" + role, "邮箱:" + userDTO.getEmail());
            LogUtil.logPerformance("创建用户", duration, userDTO.getUsername(), role);
            return Result.success("用户创建成功");
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            LogUtil.logSystemError("用户管理", "创建用户", e.getMessage(), e);
            throw e;
        }
    }
    
    @Operation(summary = "更新用户信息")
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<String> updateUser(@PathVariable Long id, @Validated @RequestBody UserVO userVO) {
        userService.updateUser(id, userVO);
        return Result.success("用户信息更新成功");
    }
    
    @Operation(summary = "删除用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<String> deleteUser(@PathVariable Long id) {
        log.warn("管理员删除用户操作 - 用户ID: {}, IP: {}", id, LogUtil.getClientIp());

        try {
            userService.deleteUser(id);
            LogUtil.logAdminOperation("删除用户", null, "超级管理员", "用户ID:" + id);
            LogUtil.logSecurityEvent("用户删除", "超级管理员", "删除用户ID:" + id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            LogUtil.logSystemError("用户管理", "删除用户", e.getMessage(), e);
            throw e;
        }
    }
    
    @Operation(summary = "启用/禁用用户")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<String> toggleUserStatus(@PathVariable Long id) {
        userService.toggleUserStatus(id);
        return Result.success("用户状态更新成功");
    }
    
    @Operation(summary = "重置用户密码")
    @PutMapping("/{id}/password")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public Result<String> resetPassword(@PathVariable Long id, @RequestParam String newPassword) {
        userService.resetPassword(id, newPassword);
        return Result.success("密码重置成功");
    }
}
