import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 生成密码哈希
        String password = "admin123";
        String hashedPassword = encoder.encode(password);
        
        System.out.println("原始密码: " + password);
        System.out.println("BCrypt哈希: " + hashedPassword);
        
        // 验证密码
        boolean matches = encoder.matches(password, hashedPassword);
        System.out.println("密码验证: " + matches);
        
        // 生成多个常用密码的哈希
        String[] passwords = {"admin123", "123456", "password", "admin"};
        
        System.out.println("\n=== 常用密码哈希值 ===");
        for (String pwd : passwords) {
            String hash = encoder.encode(pwd);
            System.out.println(pwd + " -> " + hash);
        }
    }
}
