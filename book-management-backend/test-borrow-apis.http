# 测试借阅相关API的HTTP请求文件
# 使用VS Code的REST Client插件或其他HTTP客户端工具

### 1. 登录获取Token (替换为实际的用户名和密码)
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

### 2. 获取当前借阅数量
GET http://localhost:8080/api/borrow/current-count
Authorization: Bearer {{token}}

### 3. 获取借阅记录列表
GET http://localhost:8080/api/borrow/records?current=1&size=10&status=BORROWED
Authorization: Bearer {{token}}

### 4. 借阅图书 (替换为实际的图书ID)
POST http://localhost:8080/api/borrow/borrow
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "bookId": 1,
  "remarks": "测试借阅"
}

### 5. 归还图书 (替换为实际的借阅记录ID)
POST http://localhost:8080/api/borrow/return/1
Authorization: Bearer {{token}}

### 6. 续借图书 (替换为实际的借阅记录ID)
POST http://localhost:8080/api/borrow/renew/1
Authorization: Bearer {{token}}

### 7. 获取所有借阅记录
GET http://localhost:8080/api/borrow/records?current=1&size=10
Authorization: Bearer {{token}}

### 8. 检查是否有逾期图书
GET http://localhost:8080/api/borrow/has-overdue
Authorization: Bearer {{token}}

### 注意事项:
# 1. 替换 {{token}} 为步骤1中获取的实际token
# 2. 替换图书ID和借阅记录ID为实际存在的ID
# 3. 确保数据库中有相应的测试数据
# 4. 检查后端服务是否在localhost:8080上运行
