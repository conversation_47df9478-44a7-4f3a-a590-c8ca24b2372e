{"name": "book-management-fronted", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "dayjs": "^1.11.13", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}