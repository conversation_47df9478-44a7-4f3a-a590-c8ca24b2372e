import { createRouter, createWebHistory } from 'vue-router';
import type { RouteRecordRaw } from 'vue-router';
import { useUserStore } from '@/stores/user';

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue')
  },
  {
    path: '/admin',
    component: () => import('@/layouts/AdminLayout.vue'),
    redirect: '/admin/dashboard',
    meta: { requiresAuth: true, roles: ['ADMIN', 'SUPER_ADMIN'] },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { requiresAuth: true, roles: ['ADMIN', 'SUPER_ADMIN'] }
      },
      {
        path: 'users',
        name: 'UserManagement',
        component: () => import('@/views/admin/UserManagement.vue'),
        meta: { requiresAuth: true, roles: ['SUPER_ADMIN'] }
      },
      {
        path: 'books',
        name: 'BookManagement',
        component: () => import('@/views/admin/BookManagement.vue'),
        meta: { requiresAuth: true, roles: ['ADMIN', 'SUPER_ADMIN'] }
      },
      {
        path: 'borrows',
        name: 'BorrowManagement',
        component: () => import('@/views/admin/BorrowManagement.vue'),
        meta: { requiresAuth: true, roles: ['ADMIN', 'SUPER_ADMIN'] }
      }
    ]
  },
  {
    path: '/user',
    component: () => import('@/views/UserDashboard.vue'),
    redirect: '/user/dashboard',
    meta: { requiresAuth: true, roles: ['USER'] },
    children: [
      {
        path: 'dashboard',
        name: 'UserDashboardHome',
        component: () => import('@/views/user/UserDashboardHome.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'books',
        name: 'UserBookList',
        component: () => import('@/views/user/BookList.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'search',
        name: 'UserBookSearch',
        component: () => import('@/views/user/BookSearch.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'borrowed',
        name: 'UserBorrowedBooks',
        component: () => import('@/views/user/BorrowedBooks.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'favorites',
        name: 'UserFavorites',
        component: () => import('@/views/user/UserFavorites.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/user/UserProfile.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      },
      {
        path: 'test-api',
        name: 'ApiTest',
        component: () => import('@/views/test/ApiTest.vue'),
        meta: { requiresAuth: true, roles: ['USER'] }
      }
    ]
  },
  // 兼容旧的dashboard路由
  {
    path: '/dashboard',
    redirect: '/admin/dashboard'
  }
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 公开页面，不需要认证
  const publicPages = ['/login', '/register', '/'];
  const isPublicPage = publicPages.includes(to.path);

  // 如果是公开页面，直接放行
  if (isPublicPage) {
    next();
    return;
  }

  // 检查是否已登录
  if (!userStore.isLoggedIn || !userStore.userInfo) {
    console.log('用户未登录，重定向到登录页');
    next('/login');
    return;
  }

  // 检查路由权限
  const requiredRoles = to.meta?.roles as string[] | undefined;
  if (requiredRoles && requiredRoles.length > 0) {
    const userRole = userStore.userInfo.role;

    // 超级管理员拥有所有权限
    if (userRole === 'SUPER_ADMIN') {
      next();
      return;
    }

    // 检查用户角色是否在允许的角色列表中
    if (!requiredRoles.includes(userRole)) {
      console.log(`用户角色 ${userRole} 无权访问 ${to.path}`);

      // 根据用户角色重定向到对应的默认页面
      if (userRole === 'USER') {
        next('/user/dashboard');
      } else if (userRole === 'ADMIN') {
        next('/admin/dashboard');
      } else {
        next('/');
      }
      return;
    }
  }

  // 角色特定的重定向逻辑
  if (to.path === '/') {
    const userRole = userStore.userInfo.role;
    if (userRole === 'USER') {
      next('/user/dashboard');
      return;
    } else if (userRole === 'ADMIN' || userRole === 'SUPER_ADMIN') {
      next('/admin/dashboard');
      return;
    }
  }

  // 已登录且权限足够，继续访问
  next();
});

export default router;
