<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <div class="admin-sidebar" :class="{ 'collapsed': collapsed }">
      <!-- Logo区域 -->
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon-wrapper">
            <BookOutlined class="logo-icon" />
          </div>
          <span v-if="!collapsed" class="logo-text">图书管理系统</span>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="sidebar-menu">
        <div
            v-for="item in menuItems"
            :key="item.key"
            class="menu-item"
            :class="{ 'active': selectedKeys.includes(item.key) }"
            @click="handleMenuClick(item.key)"
        >
          <div class="menu-icon-wrapper">
            <component :is="item.icon" class="menu-icon" />
          </div>
          <span v-if="!collapsed" class="menu-text">{{ item.label }}</span>
          <div v-if="selectedKeys.includes(item.key)" class="active-indicator"></div>
        </div>
      </div>

      <!-- 侧边栏底部 -->
      <div class="sidebar-footer" v-if="!collapsed">
        <div class="footer-info">
          <div class="version">v1.0.0</div>
          <div class="copyright">© 2024 图书管理系统</div>
        </div>
      </div>
    </div>

    <!-- 右侧主内容区 -->
    <div class="admin-main" :class="{ 'collapsed': collapsed }">
      <!-- 顶部栏 -->
      <div class="admin-header">
        <div class="header-left">
          <div class="trigger-wrapper" @click="toggleCollapsed">
            <MenuUnfoldOutlined
                v-if="collapsed"
                class="trigger"
            />
            <MenuFoldOutlined
                v-else
                class="trigger"
            />
          </div>

          <!-- 面包屑导航 -->
          <div class="breadcrumb">
            <a-breadcrumb>
              <a-breadcrumb-item>
                <HomeOutlined />
                <span>首页</span>
              </a-breadcrumb-item>
              <a-breadcrumb-item>{{ getCurrentPageName() }}</a-breadcrumb-item>
            </a-breadcrumb>
          </div>
        </div>

        <div class="header-right">
          <!-- 通知图标 -->
          <div class="notification-wrapper">
            <a-badge :count="3" :offset="[10, 0]">
              <BellOutlined class="header-icon" />
            </a-badge>
          </div>

          <!-- 用户下拉菜单 -->
          <a-dropdown>
            <div class="user-info">
              <a-avatar class="user-avatar" :size="36">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <div class="user-text" v-if="!isMobile">
                <div class="user-name">{{ userStore.userInfo?.username || '管理员' }}</div>
                <div class="user-role">{{ getRoleText(userStore.userInfo?.role) }}</div>
              </div>
              <DownOutlined class="dropdown-icon" v-if="!isMobile" />
            </div>
            <template #overlay>
              <a-menu class="user-menu">
                <a-menu-item key="profile">
                  <UserOutlined />
                  个人资料
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  系统设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="admin-content">
        <div class="content-wrapper">
          <router-view v-slot="{ Component }">
            <transition name="fade-slide" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div
        v-if="isMobile && !collapsed"
        class="mobile-mask"
        @click="collapsed = true"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { useUserStore } from '@/stores/user';
import {
  BookOutlined,
  DashboardOutlined,
  UserOutlined,
  SwapOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  LogoutOutlined,
  BellOutlined,
  HomeOutlined,
  DownOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

const collapsed = ref(false);
const windowWidth = ref(window.innerWidth);

// 响应式判断
const isMobile = computed(() => windowWidth.value < 768);

// 当前选中的菜单项
const selectedKeys = ref(['dashboard']);

// 菜单项配置
const menuItems = computed(() => [
  {
    key: 'dashboard',
    label: '仪表盘',
    icon: DashboardOutlined,
    show: userStore.canAccessAdmin
  },
  {
    key: 'users',
    label: '用户管理',
    icon: UserOutlined,
    show: userStore.canAccessUserManagement
  },
  {
    key: 'books',
    label: '图书管理',
    icon: BookOutlined,
    show: userStore.canAccessBookManagement
  },
  {
    key: 'borrows',
    label: '借阅管理',
    icon: SwapOutlined,
    show: userStore.canAccessBorrowManagement
  }
].filter(item => item.show));

// 获取当前页面名称
const getCurrentPageName = () => {
  const currentItem = menuItems.value.find(item => selectedKeys.value.includes(item.key));
  return currentItem?.label || '首页';
};

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  if (isMobile.value) {
    collapsed.value = true;
  }
};

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
};

// 处理菜单点击
const handleMenuClick = (key: string) => {
  selectedKeys.value = [key];

  // 移动端点击菜单后自动收起
  if (isMobile.value) {
    collapsed.value = true;
  }

  // 路由跳转
  switch (key) {
    case 'dashboard':
      router.push('/admin/dashboard');
      break;
    case 'users':
      router.push('/admin/users');
      break;
    case 'books':
      router.push('/admin/books');
      break;
    case 'borrows':
      router.push('/admin/borrows');
      break;
  }
};

// 获取角色文本
const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超级管理员',
    'ADMIN': '管理员',
    'USER': '普通用户'
  };
  return roleMap[role || ''] || '未知角色';
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  message.success('退出登录成功');
  router.push('/login');
};

// 根据当前路由设置选中的菜单项
const updateSelectedKeys = () => {
  const path = route.path;
  if (path === '/admin/dashboard' || path === '/dashboard') {
    selectedKeys.value = ['dashboard'];
  } else if (path.includes('/admin/users')) {
    selectedKeys.value = ['users'];
  } else if (path.includes('/admin/books')) {
    selectedKeys.value = ['books'];
  } else if (path.includes('/admin/borrows')) {
    selectedKeys.value = ['borrows'];
  }
};

// 监听路由变化
watch(() => route.path, () => {
  updateSelectedKeys();
});

onMounted(() => {
  window.addEventListener('resize', handleResize);
  updateSelectedKeys();

  // 移动端默认收起侧边栏
  if (isMobile.value) {
    collapsed.value = true;
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 左侧导航栏 */
.admin-sidebar {
  width: 260px;
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-sidebar.collapsed {
  width: 80px;
}

/* Logo区域 */
.sidebar-header {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.logo {
  display: flex;
  align-items: center;
  color: white;
}

.logo-icon-wrapper {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.logo-icon {
  font-size: 20px;
  color: white;
}

.logo-text {
  margin-left: 16px;
  font-size: 18px;
  font-weight: 700;
  white-space: nowrap;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航菜单 */
.sidebar-menu {
  padding: 20px 12px;
  flex: 1;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  margin: 6px 0;
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  position: relative;
  backdrop-filter: blur(10px);
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(4px);
}

.menu-item.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.menu-icon-wrapper {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.menu-item.active .menu-icon-wrapper {
  background: rgba(255, 255, 255, 0.2);
}

.menu-icon {
  font-size: 16px;
  min-width: 16px;
}

.menu-text {
  margin-left: 12px;
  white-space: nowrap;
  font-weight: 500;
}

.active-indicator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.5);
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.footer-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.version {
  font-size: 12px;
  margin-bottom: 4px;
}

.copyright {
  font-size: 10px;
}

/* 右侧主内容区 */
.admin-main {
  flex: 1;
  margin-left: 260px;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
}

.admin-main.collapsed {
  margin-left: 80px;
}

/* 顶部栏 */
.admin-header {
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.trigger-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(59, 130, 246, 0.1);
}

.trigger-wrapper:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

.trigger {
  font-size: 18px;
  color: #3b82f6;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notification-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(59, 130, 246, 0.1);
}

.notification-wrapper:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(1.05);
}

.header-icon {
  font-size: 16px;
  color: #3b82f6;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(59, 130, 246, 0.1);
}

.user-info:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.user-avatar {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.user-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e40af;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #64748b;
  line-height: 1.2;
}

.dropdown-icon {
  font-size: 12px;
  color: #64748b;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.user-menu {
  min-width: 160px;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

/* 内容区域 */
.admin-content {
  flex: 1;
  padding: 32px;
  overflow-x: auto;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 移动端遮罩 */
.mobile-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(4px);
}

/* 滚动条样式 */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .admin-main {
    margin-left: 0;
  }

  .admin-main.collapsed {
    margin-left: 0;
  }

  .admin-sidebar {
    transform: translateX(-100%);
  }

  .admin-sidebar:not(.collapsed) {
    transform: translateX(0);
  }

  .admin-content {
    padding: 20px 16px;
  }

  .admin-header {
    padding: 0 16px;
    height: 64px;
  }

  .header-left {
    gap: 16px;
  }

  .header-right {
    gap: 12px;
  }

  .breadcrumb {
    display: none;
  }
}

/* 平板样式 */
@media (max-width: 1024px) and (min-width: 769px) {
  .admin-content {
    padding: 24px 20px;
  }

  .admin-header {
    padding: 0 24px;
  }
}

/* 响应式动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>