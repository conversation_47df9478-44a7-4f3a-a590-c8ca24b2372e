// 通用类型定义

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface PageResult<T = any> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  realName: string;
  phone?: string;
  role: 'SUPER_ADMIN' | 'ADMIN' | 'USER';
  status: number; // 后端使用数字：0-禁用，1-启用
  avatar?: string;
  lastLoginTime?: string;
  createdTime: string;
  updatedTime?: string;
}

export interface LoginForm {
  username: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
  realName: string;
  phone?: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// 图书相关类型
export interface Book {
  id: number;
  isbn?: string;
  title: string;
  author: string;
  publisher?: string;
  publishDate?: string;
  categoryId?: number;
  category?: string; // 用于用户端显示
  categoryName?: string;
  description?: string;
  coverUrl?: string;
  price?: number;
  totalQuantity: number;
  availableQuantity: number;
  status: 'AVAILABLE' | 'BORROWED' | 'RESERVED' | number; // 支持字符串和数字状态
  createdAt?: string; // 用户端字段名
  updatedAt?: string; // 用户端字段名
  createdTime?: string; // 管理端字段名
  updatedTime?: string; // 管理端字段名
}

export interface BookForm {
  isbn?: string;
  title: string;
  author: string;
  publisher?: string;
  publishDate?: string;
  categoryId: number;
  description?: string;
  coverUrl?: string;
  price?: number;
  totalQuantity: number;
  availableQuantity: number;
  status?: number;
}

// 图书分类相关类型
export interface BookCategory {
  id: number;
  name: string;
  parentId: number;
  parentName?: string;
  description?: string;
  sortOrder: number;
  status: number;
  createdTime: string;
  updatedTime: string;
  children?: BookCategory[];
}

export interface BookCategoryForm {
  name: string;
  parentId: number;
  description?: string;
  sortOrder?: number;
  status?: number;
}

// 借阅记录相关类型
export interface BorrowRecord {
  id: number;
  userId: number;
  username: string;
  userRealName: string;
  bookId: number;
  book: {
    id: number;
    title: string;
    author: string;
    isbn?: string;
    publisher?: string;
    category?: string;
  };
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: 'BORROWED' | 'RETURNED' | 'OVERDUE';
  overdueDays: number;
  fineAmount: number;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BorrowForm {
  bookId: number;
  remarks?: string;
}

// 分页结果类型
export interface PageResult<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: string;
  path?: string;
  children?: MenuItem[];
  roles?: string[];
}
