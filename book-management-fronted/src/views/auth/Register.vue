<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>创建您的账号</p>
      </div>
      
      <a-form
        :model="registerForm"
        :rules="rules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
        role="form"
        aria-label="用户注册表单"
      >
        <a-form-item name="username">
          <label for="reg-username" class="form-label">用户名</label>
          <div class="input-wrapper">
            <UserOutlined class="input-icon" />
            <a-input
              id="reg-username"
              v-model:value="registerForm.username"
              class="custom-input"
              aria-label="请输入用户名"
              aria-required="true"
              autocomplete="username"
            />
          </div>
        </a-form-item>

        <a-form-item name="email">
          <label for="reg-email" class="form-label">邮箱</label>
          <div class="input-wrapper">
            <MailOutlined class="input-icon" />
            <a-input
              id="reg-email"
              v-model:value="registerForm.email"
              class="custom-input"
              aria-label="请输入邮箱地址"
              aria-required="true"
              autocomplete="email"
              type="email"
            />
          </div>
        </a-form-item>

        <a-form-item name="realName">
          <label for="reg-realname" class="form-label">真实姓名</label>
          <div class="input-wrapper">
            <IdcardOutlined class="input-icon" />
            <a-input
              id="reg-realname"
              v-model:value="registerForm.realName"
              class="custom-input"
              aria-label="请输入真实姓名"
              aria-required="true"
              autocomplete="name"
            />
          </div>
        </a-form-item>

        <a-form-item name="phone">
          <label for="reg-phone" class="form-label">手机号（可选）</label>
          <div class="input-wrapper">
            <PhoneOutlined class="input-icon" />
            <a-input
              id="reg-phone"
              v-model:value="registerForm.phone"
              class="custom-input"
              aria-label="请输入手机号，此项为可选"
              autocomplete="tel"
              type="tel"
            />
          </div>
        </a-form-item>

        <a-form-item name="password">
          <label for="reg-password" class="form-label">密码</label>
          <div class="input-wrapper">
            <LockOutlined class="input-icon" />
            <a-input-password
              id="reg-password"
              v-model:value="registerForm.password"
              class="custom-input"
              aria-label="请输入密码"
              aria-required="true"
              autocomplete="new-password"
            />
          </div>
        </a-form-item>

        <a-form-item name="confirmPassword">
          <label for="reg-confirm-password" class="form-label">确认密码</label>
          <div class="input-wrapper">
            <LockOutlined class="input-icon" />
            <a-input-password
              id="reg-confirm-password"
              v-model:value="registerForm.confirmPassword"
              class="custom-input"
              aria-label="请再次输入密码进行确认"
              aria-required="true"
              autocomplete="new-password"
            />
          </div>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="loading"
            class="register-button"
            aria-label="点击注册"
          >
            注册
          </a-button>
        </a-form-item>

        <div class="register-footer">
          <span class="footer-text">已有账号？</span>
          <router-link to="/login" class="login-link" aria-label="前往登录页面">立即登录</router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h } from 'vue';
import { useRouter } from 'vue-router';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  IdcardOutlined,
  PhoneOutlined
} from '@ant-design/icons-vue';

const router = useRouter();

const loading = ref(false);
const registerForm = ref({
  username: '',
  password: '',
  confirmPassword: '',
  email: '',
  realName: '',
  phone: ''
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度必须在3-20个字符之间', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '邮箱格式不正确', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 50, message: '真实姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度必须在6-20个字符之间', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value !== registerForm.value.password) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

const handleRegister = async () => {
  loading.value = true;
  try {
    console.log('注册表单:', registerForm.value);
    alert('注册功能开发中...');
    router.push('/login');
  } catch (error) {
    console.error('注册失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 确保页面占满整个屏幕 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.register-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.register-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 10% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 90% 30%, rgba(255, 255, 255, 0.04) 0%, transparent 30%);
  pointer-events: none;
}

.register-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.register-box {
  width: 100%;
  max-width: 540px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 48px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  max-height: 90vh;
  overflow-y: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 36px;
}

.register-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.register-header p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

.register-form {
  font-size: 18px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  line-height: 1.5;
}

.register-form :deep(.ant-form-item) {
  margin-bottom: 20px;
}

/* 自定义输入框包装容器 */
.input-wrapper {
  display: flex;
  align-items: center;
  height: 48px;
  border-radius: 12px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 输入框图标样式 */
.input-icon {
  margin-right: 12px;
  color: #9ca3af;
  font-size: 16px;
  transition: color 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
  color: #667eea;
}

/* 移除 Ant Design 输入框的原生样式 */
.register-form :deep(.ant-input),
.register-form :deep(.ant-input-password) {
  flex: 1;
  font-size: 16px;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  height: auto !important;
}

.register-form :deep(.ant-input:focus),
.register-form :deep(.ant-input-password:focus),
.register-form :deep(.ant-input-focused),
.register-form :deep(.ant-input-password-focused) {
  border: none !important;
  box-shadow: none !important;
}

/* 移除 Ant Design 的前缀图标 */
.register-form :deep(.ant-input-prefix),
.register-form :deep(.ant-input-password-prefix) {
  display: none !important;
}

/* 密码输入框的显示/隐藏按钮样式 */
.register-form :deep(.ant-input-password-icon) {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.input-wrapper:focus-within :deep(.ant-input-password-icon) {
  color: #667eea;
}

.register-button {
  height: 52px !important;
  border-radius: 12px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
  margin-top: 8px;
}

.register-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5) !important;
}

.register-footer {
  text-align: center;
  margin-top: 28px;
  font-size: 16px;
}

.footer-text {
  color: #64748b;
  font-weight: 400;
}

.login-link {
  color: #667eea !important;
  text-decoration: none !important;
  margin-left: 8px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.login-link:hover {
  color: #4f46e5 !important;
  text-decoration: none !important;
}

@media (max-width: 768px) {
  .register-container {
    padding: 16px;
  }

  .register-box {
    padding: 36px 28px;
    max-width: 480px;
  }

  .register-header h1 {
    font-size: 28px;
  }

  .register-header p {
    font-size: 16px;
  }

  .input-wrapper {
    height: 44px;
    padding: 0 14px;
  }

  .input-icon {
    font-size: 15px;
    margin-right: 10px;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 12px;
  }

  .register-box {
    padding: 32px 24px;
    max-width: 380px;
  }

  .register-header h1 {
    font-size: 24px;
  }

  .register-form :deep(.ant-form-item) {
    margin-bottom: 18px;
  }

  .input-wrapper {
    height: 42px;
    padding: 0 12px;
  }

  .input-icon {
    font-size: 14px;
    margin-right: 8px;
  }

  .register-form :deep(.ant-input),
  .register-form :deep(.ant-input-password) {
    font-size: 15px;
  }
}
</style>
