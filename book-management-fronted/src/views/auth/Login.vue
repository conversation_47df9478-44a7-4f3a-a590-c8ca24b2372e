<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>图书管理系统</h1>
        <p>欢迎登录</p>
      </div>
      
      <a-form
        :model="loginForm"
        :rules="rules"
        @finish="handleLogin"
        layout="vertical"
        class="login-form"
        role="form"
        aria-label="用户登录表单"
      >
        <a-form-item name="username">
          <label for="username" class="form-label">用户名</label>
          <div class="input-wrapper">
            <UserOutlined class="input-icon" />
            <a-input
              id="username"
              v-model:value="loginForm.username"
              class="custom-input"
              aria-label="请输入用户名"
              aria-required="true"
              autocomplete="username"
            />
          </div>
        </a-form-item>

        <a-form-item name="password">
          <label for="password" class="form-label">密码</label>
          <div class="input-wrapper">
            <LockOutlined class="input-icon" />
            <a-input-password
              id="password"
              v-model:value="loginForm.password"
              class="custom-input"
              aria-label="请输入密码"
              aria-required="true"
              autocomplete="current-password"
            />
          </div>
        </a-form-item>

        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="loading"
            class="login-button"
            aria-label="点击登录"
          >
            登录
          </a-button>
        </a-form-item>

        <div class="login-footer">
          <span class="footer-text">还没有账号？</span>
          <router-link to="/register" class="register-link" aria-label="前往注册页面">立即注册</router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { login } from '@/api/auth';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const loginForm = ref({
  username: '',
  password: ''
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
};

const handleLogin = async () => {
  loading.value = true;
  try {
    console.log('登录表单:', loginForm.value);

    // 使用store的登录方法
    await userStore.loginAction(loginForm.value);

    // 登录成功
    message.success('登录成功！');

    // 根据用户角色跳转到对应页面
    const userRole = userStore.userInfo?.role;
    console.log('用户角色:', userRole);

    if (userRole === 'USER') {
      router.push('/user/books');
    } else if (userRole === 'ADMIN' || userRole === 'SUPER_ADMIN') {
      router.push('/admin/dashboard');
    } else {
      router.push('/');
    }
  } catch (error: any) {
    console.error('登录失败:', error);
    // 显示具体的错误信息
    if (error.message) {
      message.error(error.message);
    } else {
      message.error('登录失败，请检查网络连接');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 确保页面占满整个屏幕 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(255, 255, 255, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 10% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 40%),
    radial-gradient(circle at 90% 30%, rgba(255, 255, 255, 0.04) 0%, transparent 30%);
  pointer-events: none;
}

.login-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  pointer-events: none;
}

.login-box {
  width: 100%;
  max-width: 420px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 48px;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
}

.login-header p {
  font-size: 18px;
  color: #64748b;
  margin: 0;
  font-weight: 400;
}

.login-form {
  font-size: 18px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  line-height: 1.5;
}

.login-form :deep(.ant-form-item) {
  margin-bottom: 24px;
}

/* 自定义输入框包装容器 */
.input-wrapper {
  display: flex;
  align-items: center;
  height: 48px;
  border-radius: 12px;
  padding: 0 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 输入框图标样式 */
.input-icon {
  margin-right: 12px;
  color: #9ca3af;
  font-size: 16px;
  transition: color 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
  color: #667eea;
}

/* 移除 Ant Design 输入框的原生样式 */
.login-form :deep(.ant-input),
.login-form :deep(.ant-input-password) {
  flex: 1;
  font-size: 16px;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  height: auto !important;
}

.login-form :deep(.ant-input:focus),
.login-form :deep(.ant-input-password:focus),
.login-form :deep(.ant-input-focused),
.login-form :deep(.ant-input-password-focused) {
  border: none !important;
  box-shadow: none !important;
}

/* 移除 Ant Design 的前缀图标 */
.login-form :deep(.ant-input-prefix),
.login-form :deep(.ant-input-password-prefix) {
  display: none !important;
}

/* 密码输入框的显示/隐藏按钮样式 */
.login-form :deep(.ant-input-password-icon) {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.input-wrapper:focus-within :deep(.ant-input-password-icon) {
  color: #667eea;
}

.login-button {
  height: 52px !important;
  border-radius: 12px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4) !important;
  transition: all 0.3s ease !important;
  margin-top: 8px;
}

.login-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.5) !important;
}

.login-footer {
  text-align: center;
  margin-top: 32px;
  font-size: 16px;
}

.footer-text {
  color: #64748b;
  font-weight: 400;
}

.register-link {
  color: #667eea !important;
  text-decoration: none !important;
  margin-left: 8px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.register-link:hover {
  color: #4f46e5 !important;
  text-decoration: none !important;
}

@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-box {
    padding: 36px 28px;
    max-width: 380px;
  }

  .login-header h1 {
    font-size: 28px;
  }

  .login-header p {
    font-size: 16px;
  }

  .input-wrapper {
    height: 44px;
    padding: 0 14px;
  }

  .input-icon {
    font-size: 15px;
    margin-right: 10px;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .login-box {
    padding: 32px 24px;
    max-width: 340px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .input-wrapper {
    height: 42px;
    padding: 0 12px;
  }

  .input-icon {
    font-size: 14px;
    margin-right: 8px;
  }

  .login-form :deep(.ant-input),
  .login-form :deep(.ant-input-password) {
    font-size: 15px;
  }
}
</style>
