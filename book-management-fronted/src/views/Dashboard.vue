<template>
  <div class="dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-card :bordered="false">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>欢迎回来，管理员！</h1>
            <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          </div>
          <div class="welcome-avatar">
            <a-avatar :size="80">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计数据区域 -->
    <div class="stats-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="图书总数"
              :value="stats.totalBooks"
              :prefix="h(BookOutlined)"
              :value-style="{ color: '#667eea' }"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="可借图书"
              :value="stats.availableBooks"
              :prefix="h(CheckCircleOutlined)"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="借阅中"
              :value="stats.borrowedBooks"
              :prefix="h(SwapOutlined)"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :lg="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="逾期图书"
              :value="stats.overdueBooks"
              :prefix="h(ExclamationCircleOutlined)"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="12">
          <a-card title="最新图书" :bordered="false">
            <a-list
              :data-source="recentBooks"
              :loading="loading"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.title"
                    :description="`作者：${item.author} | 分类：${item.categoryName}`"
                  >
                    <template #avatar>
                      <a-avatar shape="square" :size="48">
                        <template #icon>
                          <BookOutlined />
                        </template>
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                  <div class="book-status">
                    <a-tag v-if="item.status === 1" color="green">上架</a-tag>
                    <a-tag v-else color="red">下架</a-tag>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="12">
          <a-card title="我的借阅" :bordered="false">
            <a-list
              :data-source="myBorrows"
              :loading="loading"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.bookTitle"
                    :description="`借阅时间：${formatDate(item.borrowTime)}`"
                  >
                    <template #avatar>
                      <a-avatar shape="square" :size="48">
                        <template #icon>
                          <SwapOutlined />
                        </template>
                      </a-avatar>
                    </template>
                  </a-list-item-meta>
                  <div class="borrow-status">
                    <a-tag v-if="item.status === 'BORROWED'" color="blue">借阅中</a-tag>
                    <a-tag v-else-if="item.status === 'OVERDUE'" color="red">逾期</a-tag>
                    <a-tag v-else color="green">已归还</a-tag>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  UserOutlined,
  BookOutlined,
  CheckCircleOutlined,
  SwapOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import { getDashboardStats, getPopularBooks, getRecentActivities } from '@/api/dashboard';

const loading = ref(false);
const currentDate = ref(dayjs().format('YYYY年MM月DD日'));

// 统计数据
const stats = ref({
  totalBooks: 0,
  availableBooks: 0,
  borrowedBooks: 0,
  overdueBooks: 0
});

// 最新图书
const recentBooks = ref([
  {
    id: 1,
    title: '红楼梦',
    author: '曹雪芹',
    categoryName: '小说',
    status: 1
  },
  {
    id: 2,
    title: 'Java核心技术',
    author: 'Cay S. Horstmann',
    categoryName: '计算机',
    status: 1
  },
  {
    id: 3,
    title: '平凡的世界',
    author: '路遥',
    categoryName: '小说',
    status: 1
  }
]);

// 我的借阅
const myBorrows = ref([
  {
    id: 1,
    bookTitle: '数据结构与算法分析',
    borrowTime: '2024-01-15T10:00:00',
    status: 'BORROWED'
  },
  {
    id: 2,
    bookTitle: '高等数学',
    borrowTime: '2024-01-10T14:30:00',
    status: 'BORROWED'
  }
]);

const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm');
};

const loadDashboardData = async () => {
  loading.value = true;
  try {
    // 尝试调用真实API获取仪表盘数据
    const response = await getDashboardStats();
    if (response.code === 200) {
      const data = response.data;
      stats.value = {
        totalBooks: data.bookStats.totalBooks,
        availableBooks: data.bookStats.availableBooks,
        borrowedBooks: data.bookStats.borrowedBooks,
        overdueBooks: data.borrowStats.overdueCount
      };
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    console.error('获取仪表盘数据失败:', error.message);
    // 显示错误状态，不使用错误的模拟数据
    stats.value = {
      totalBooks: 0,
      availableBooks: 0,
      borrowedBooks: 0,
      overdueBooks: 0
    };
    // 可以在这里添加错误提示
    message.error('获取仪表盘数据失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped>
.dashboard {
  background: transparent;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.welcome-section {
  margin-bottom: 16px;
}

.welcome-section {
  margin-bottom: 24px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
}

.welcome-text p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.stats-section {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.content-section {
  margin-bottom: 16px;
}

.book-status,
.borrow-status {
  display: flex;
  align-items: center;
}

.dashboard :deep(.ant-statistic-title) {
  font-size: 16px;
  font-weight: 500;
}

.dashboard :deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

.dashboard :deep(.ant-card-head-title) {
  font-size: 18px;
  font-weight: 600;
}

.dashboard :deep(.ant-list-item-meta-title) {
  font-size: 16px;
  font-weight: 500;
}

.dashboard :deep(.ant-list-item-meta-description) {
  font-size: 14px;
}

/* 桌面端大屏优化 */
@media (min-width: 1200px) {
  .welcome-section {
    margin-bottom: 20px;
  }

  .stats-section {
    margin-bottom: 20px;
  }

  .content-section {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }

  .welcome-avatar {
    margin-top: 16px;
  }

  .welcome-text h1 {
    font-size: 20px;
  }
}
</style>
