<template>
  <div class="book-management">
    <!-- 页面标题和操作区 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>图书管理</h1>
          <p>管理图书馆中的所有图书信息</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" @click="showAddModal">
            <template #icon>
              <PlusOutlined />
            </template>
            添加图书
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.title"
            placeholder="请输入书名"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.author"
            placeholder="请输入作者"
            allow-clear
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.categoryId"
            placeholder="请选择分类"
            allow-clear
            style="width: 100%"
          >
            <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option :value="1">上架</a-select-option>
            <a-select-option :value="0">下架</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 图书列表表格 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="filteredBooks"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1000 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'cover'">
            <a-avatar shape="square" :size="50">
              <template #icon>
                <BookOutlined />
              </template>
            </a-avatar>
          </template>

          <template v-else-if="column.key === 'category'">
            <a-tag color="blue">{{ getCategoryName(record.categoryId) }}</a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '上架' : '下架' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'stock'">
            <span :class="{ 'low-stock': record.stock <= 5 }">
              {{ record.stock }}
            </span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :danger="record.status === 1"
                @click="toggleBookStatus(record)"
              >
                {{ record.status === 1 ? '下架' : '上架' }}
              </a-button>
              <a-popconfirm
                title="确定要删除这本图书吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteBook(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑图书模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑图书' : '添加图书'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="700px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="book-form"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="书名" name="title">
              <a-input v-model:value="formData.title" placeholder="请输入书名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="作者" name="author">
              <a-input v-model:value="formData.author" placeholder="请输入作者" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="ISBN" name="isbn">
              <a-input v-model:value="formData.isbn" placeholder="请输入ISBN" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="出版社" name="publisher">
              <a-input v-model:value="formData.publisher" placeholder="请输入出版社" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="分类" name="categoryId">
              <a-select v-model:value="formData.categoryId" placeholder="请选择分类">
                <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="库存数量" name="stock">
              <a-input-number
                v-model:value="formData.stock"
                :min="0"
                :max="9999"
                placeholder="库存数量"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">上架</a-select-option>
                <a-select-option :value="0">下架</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="图书简介" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入图书简介"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance, TableColumnsType } from 'ant-design-vue';
import {
  PlusOutlined,
  BookOutlined,
  SearchOutlined,
  UserOutlined
} from '@ant-design/icons-vue';
import { getBookList, createBook, updateBook, deleteBook as deleteBookApi, toggleBookStatus as toggleBookStatusApi, getBookCategories } from '@/api/book';
import { mockBookList } from '@/api/mock';
import type { Book, BookForm, PageResult, BookCategory } from '@/types';

// 使用全局类型定义

// 搜索表单类型
interface SearchForm {
  title?: string;
  author?: string;
  categoryId?: number;
  status?: number;
}

// 响应式数据
const loading = ref(false);
const modalVisible = ref(false);
const modalLoading = ref(false);
const isEdit = ref(false);
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive<SearchForm>({
  title: '',
  author: '',
  categoryId: undefined,
  status: undefined
});

// 图书表单数据
const formData = reactive<BookForm>({
  title: '',
  author: '',
  isbn: '',
  publisher: '',
  categoryId: undefined,
  stock: 0,
  status: 1,
  description: ''
});

// 图书分类数据
const categories = ref<BookCategory[]>([]);

// 图书数据
const books = ref<Book[]>([]);
const bookPageData = ref<PageResult<Book>>({
  records: [],
  total: 0,
  current: 1,
  size: 10,
  pages: 0
});

// 获取图书列表数据
const fetchBookList = async () => {
  try {
    loading.value = true;

    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      keyword: searchForm.title || searchForm.author || undefined,
      categoryId: searchForm.categoryId || undefined,
      status: searchForm.status
    };

    // 尝试调用真实API
    const response = await getBookList(params);

    if (response.code === 200) {
      bookPageData.value = response.data;
      books.value = response.data.records;
      pagination.total = response.data.total;
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    console.warn('获取图书列表失败，使用模拟数据:', error.message);
    // 使用模拟数据
    bookPageData.value = mockBookList;
    books.value = mockBookList.records;
    pagination.total = mockBookList.total;
  } finally {
    loading.value = false;
  }
};

// 获取图书分类数据
const fetchCategories = async () => {
  try {
    const response = await getBookCategories();
    if (response.code === 200) {
      categories.value = response.data;
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    console.warn('获取分类失败，使用默认分类:', error.message);
    // 使用默认分类
    categories.value = [
      { id: 1, name: '计算机技术', parentId: 0, description: '', sortOrder: 1, status: 1, createdTime: '', updatedTime: '' }
    ];
  }
};

// 当前编辑的图书ID
const currentBookId = ref<number | null>(null);

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '封面',
    key: 'cover',
    width: 80,
    align: 'center'
  },
  {
    title: '书名',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '作者',
    dataIndex: 'author',
    key: 'author',
    width: 150
  },
  {
    title: 'ISBN',
    dataIndex: 'isbn',
    key: 'isbn',
    width: 140
  },
  {
    title: '出版社',
    dataIndex: 'publisher',
    key: 'publisher',
    width: 150
  },
  {
    title: '分类',
    key: 'category',
    width: 100,
    align: 'center'
  },
  {
    title: '库存',
    key: 'stock',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入书名', trigger: 'blur' },
    { max: 100, message: '书名不能超过100个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者', trigger: 'blur' },
    { max: 50, message: '作者不能超过50个字符', trigger: 'blur' }
  ],
  isbn: [
    { required: true, message: '请输入ISBN', trigger: 'blur' },
    { pattern: /^(97[89])?\d{9}[\dX]$/, message: '请输入正确的ISBN格式', trigger: 'blur' }
  ],
  publisher: [
    { required: true, message: '请输入出版社', trigger: 'blur' },
    { max: 100, message: '出版社不能超过100个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  stock: [
    { required: true, message: '请输入库存数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '库存数量不能小于0', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 计算属性：过滤后的图书列表
const filteredBooks = computed(() => {
  let result = books.value;

  if (searchForm.title) {
    result = result.filter(book =>
      book.title.toLowerCase().includes(searchForm.title!.toLowerCase())
    );
  }

  if (searchForm.author) {
    result = result.filter(book =>
      book.author.toLowerCase().includes(searchForm.author!.toLowerCase())
    );
  }

  if (searchForm.categoryId) {
    result = result.filter(book => book.categoryId === searchForm.categoryId);
  }

  if (searchForm.status !== undefined) {
    result = result.filter(book => book.status === searchForm.status);
  }

  // 更新分页总数
  pagination.total = result.length;

  // 分页处理
  const start = (pagination.current - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;

  return result.slice(start, end);
});

// 获取分类名称
const getCategoryName = (categoryId: number) => {
  const category = categories.value.find(cat => cat.id === categoryId);
  return category ? category.name : '未知分类';
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchBookList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    title: '',
    author: '',
    categoryId: undefined,
    status: undefined
  });
  pagination.current = 1;
  fetchBookList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchBookList();
};

// 显示添加模态框
const showAddModal = () => {
  isEdit.value = false;
  modalVisible.value = true;
  resetForm();
};

// 显示编辑模态框
const showEditModal = (record: Book) => {
  isEdit.value = true;
  currentBookId.value = record.id;
  modalVisible.value = true;

  // 填充表单数据
  Object.assign(formData, {
    title: record.title,
    author: record.author,
    isbn: record.isbn,
    publisher: record.publisher,
    categoryId: record.categoryId,
    stock: record.stock,
    status: record.status,
    description: record.description || ''
  });
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    title: '',
    author: '',
    isbn: '',
    publisher: '',
    categoryId: undefined,
    stock: 0,
    status: 1,
    description: ''
  });
  formRef.value?.resetFields();
};

// 模态框确定处理
const handleModalOk = async () => {
  try {
    await formRef.value?.validate();
    modalLoading.value = true;

    if (isEdit.value) {
      // 编辑图书
      console.log('编辑图书:', { bookId: currentBookId.value, formData });

      await updateBook(currentBookId.value!, {
        title: formData.title,
        author: formData.author,
        isbn: formData.isbn,
        publisher: formData.publisher,
        publishDate: formData.publishDate,
        categoryId: formData.categoryId!,
        description: formData.description,
        coverUrl: formData.coverUrl,
        price: formData.price,
        totalQuantity: formData.totalQuantity,
        status: formData.status
      });

      message.success('图书信息更新成功');
    } else {
      // 新增图书
      console.log('创建图书:', formData);

      await createBook({
        title: formData.title,
        author: formData.author,
        isbn: formData.isbn,
        publisher: formData.publisher,
        publishDate: formData.publishDate,
        categoryId: formData.categoryId!,
        description: formData.description,
        coverUrl: formData.coverUrl,
        price: formData.price,
        totalQuantity: formData.totalQuantity,
        status: formData.status
      });

      message.success('图书创建成功');
    }

    // 重新获取图书列表
    await fetchBookList();

    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    modalLoading.value = false;
  }
};

// 模态框取消处理
const handleModalCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 切换图书状态
const toggleBookStatus = async (record: Book) => {
  try {
    loading.value = true;

    console.log('切换图书状态:', {
      bookId: record.id,
      currentStatus: record.status,
      bookTitle: record.title
    });

    // 调用真实API
    await toggleBookStatusApi(record.id);

    // 重新获取图书列表
    await fetchBookList();

    const statusText = record.status === 1 ? '下架' : '上架';
    message.success(`图书已${statusText}`);
  } catch (error: any) {
    console.error('切换图书状态失败:', error);
    message.error(error.response?.data?.message || error.message || '操作失败');
  } finally {
    loading.value = false;
  }
};

// 删除图书
const deleteBook = async (bookId: number) => {
  try {
    loading.value = true;

    console.log('删除图书:', { bookId });

    // 调用真实API
    await deleteBookApi(bookId);

    // 重新获取图书列表
    await fetchBookList();

    message.success('图书删除成功');
  } catch (error: any) {
    console.error('删除图书失败:', error);
    message.error(error.response?.data?.message || error.message || '删除失败');
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  fetchCategories();
  fetchBookList();
});
</script>

<style scoped>
.book-management {
  background: transparent;
  padding: 0;
}

.page-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-text h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.header-text p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

.search-card {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 0;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.ant-form-item:last-child) {
  margin-right: 0;
}

/* 桌面端大屏优化 */
@media (min-width: 1200px) {
  .page-header {
    margin-bottom: 20px;
  }

  .search-card {
    margin-bottom: 16px;
  }
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.book-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 库存警告样式 */
.low-stock {
  color: #ff4d4f;
  font-weight: 600;
}

/* 主题色按钮样式 */
.book-management :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.book-management :deep(.ant-btn-primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 表格样式优化 */
.book-management :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #1f2937;
}

.book-management :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8faff;
}

/* 标签样式 */
.book-management :deep(.ant-tag) {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 模态框样式 */
.book-management :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.book-management :deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.book-management :deep(.ant-modal-body) {
  padding: 24px;
}

/* 文本域样式 */
.book-management :deep(.ant-input) {
  border-radius: 6px;
}

.book-management :deep(.ant-select) {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .book-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    margin-top: 16px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form :deep(.ant-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-form :deep(.ant-form-item:last-child) {
    margin-bottom: 0;
  }

  .book-management :deep(.ant-table) {
    font-size: 14px;
  }

  .book-management :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

@media (max-width: 480px) {
  .book-management {
    padding: 12px;
  }

  .header-text h1 {
    font-size: 20px;
  }

  .book-management :deep(.ant-btn) {
    font-size: 14px;
    height: 36px;
    padding: 0 12px;
  }
}
</style>
