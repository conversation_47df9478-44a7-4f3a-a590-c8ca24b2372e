<template>
  <div class="borrow-management">
    <!-- 页面标题和操作区 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>借阅管理</h1>
          <p>管理图书借阅、归还和逾期处理</p>
        </div>
        <div class="header-actions">
          <a-space>
            <a-button type="primary" @click="showAddModal">
              <template #icon>
                <PlusOutlined />
              </template>
              新增借阅
            </a-button>
            <a-button @click="handleBatchReturn" :disabled="selectedRowKeys.length === 0">
              <template #icon>
                <CheckOutlined />
              </template>
              批量归还
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="5">
          <a-input
            v-model:value="searchForm.username"
            placeholder="请输入用户名"
            allow-clear
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="5">
          <a-input
            v-model:value="searchForm.bookTitle"
            placeholder="请输入图书名"
            allow-clear
          >
            <template #prefix>
              <BookOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option value="BORROWED">借阅中</a-select-option>
            <a-select-option value="RETURNED">已归还</a-select-option>
            <a-select-option value="OVERDUE">逾期</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-range-picker
            v-model:value="searchForm.dateRange"
            style="width: 100%"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-col>
        <a-col :span="4">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 借阅记录表格 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="filteredBorrows"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 1200 }"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userAvatar'">
            <a-avatar :size="40">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </template>

          <template v-else-if="column.key === 'bookCover'">
            <a-avatar shape="square" :size="40">
              <template #icon>
                <BookOutlined />
              </template>
            </a-avatar>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'daysLeft'">
            <span :class="getDaysLeftClass(record)">
              {{ getDaysLeft(record) }}
            </span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                v-if="record.status === 'BORROWED'"
                type="link"
                size="small"
                @click="returnBookAction(record)"
              >
                归还
              </a-button>
              <a-button
                v-if="record.status === 'OVERDUE'"
                type="link"
                size="small"
                danger
                @click="handleOverdue(record)"
              >
                处理逾期
              </a-button>
              <a-button type="link" size="small" @click="showDetailModal(record)">
                详情
              </a-button>
              <a-popconfirm
                title="确定要删除这条借阅记录吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteBorrow(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增借阅模态框 -->
    <a-modal
      v-model:open="modalVisible"
      title="新增借阅记录"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="borrow-form"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户" name="userId">
              <a-select
                v-model:value="formData.userId"
                placeholder="请选择用户"
                show-search
                :filter-option="filterUserOption"
              >
                <a-select-option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.username }} ({{ user.realName }})
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="图书" name="bookId">
              <a-select
                v-model:value="formData.bookId"
                placeholder="请选择图书"
                show-search
                :filter-option="filterBookOption"
              >
                <a-select-option v-for="book in availableBooks" :key="book.id" :value="book.id">
                  {{ book.title }} - {{ book.author }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="借阅日期" name="borrowDate">
              <a-date-picker
                v-model:value="formData.borrowDate"
                style="width: 100%"
                placeholder="请选择借阅日期"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="应还日期" name="dueDate">
              <a-date-picker
                v-model:value="formData.dueDate"
                style="width: 100%"
                placeholder="请选择应还日期"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="借阅详情"
      :footer="null"
      width="600px"
    >
      <a-descriptions v-if="currentRecord" :column="2" bordered>
        <a-descriptions-item label="用户名">{{ currentRecord.username }}</a-descriptions-item>
        <a-descriptions-item label="真实姓名">{{ currentRecord.realName }}</a-descriptions-item>
        <a-descriptions-item label="图书名称">{{ currentRecord.bookTitle }}</a-descriptions-item>
        <a-descriptions-item label="作者">{{ currentRecord.bookAuthor }}</a-descriptions-item>
        <a-descriptions-item label="借阅日期">{{ currentRecord.borrowDate }}</a-descriptions-item>
        <a-descriptions-item label="应还日期">{{ currentRecord.dueDate }}</a-descriptions-item>
        <a-descriptions-item label="实际归还日期">
          {{ currentRecord.returnDate || '未归还' }}
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">
          {{ currentRecord.remark || '无' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance, TableColumnsType } from 'ant-design-vue';
import dayjs, { type Dayjs } from 'dayjs';
import {
  PlusOutlined,
  CheckOutlined,
  UserOutlined,
  BookOutlined,
  SearchOutlined
} from '@ant-design/icons-vue';
import { getBorrowList, createBorrow, returnBook } from '@/api/borrow';
import { mockBorrowList } from '@/api/mock';
import type { BorrowRecord, BorrowForm, PageResult } from '@/types';

// 借阅记录数据类型定义
interface BorrowRecord {
  id: number;
  userId: number;
  username: string;
  realName: string;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  borrowDate: string;
  dueDate: string;
  returnDate?: string;
  status: 'BORROWED' | 'RETURNED' | 'OVERDUE';
  remark?: string;
}

// 用户数据类型
interface User {
  id: number;
  username: string;
  realName: string;
}

// 图书数据类型
interface Book {
  id: number;
  title: string;
  author: string;
  stock: number;
}

// 表单数据类型
interface BorrowForm {
  userId?: number;
  bookId?: number;
  borrowDate?: Dayjs;
  dueDate?: Dayjs;
  remark?: string;
}

// 搜索表单类型
interface SearchForm {
  username?: string;
  bookTitle?: string;
  status?: 'BORROWED' | 'RETURNED' | 'OVERDUE';
  dateRange?: [Dayjs, Dayjs];
}

// 响应式数据
const loading = ref(false);
const modalVisible = ref(false);
const detailModalVisible = ref(false);
const modalLoading = ref(false);
const formRef = ref<FormInstance>();
const selectedRowKeys = ref<number[]>([]);

// 搜索表单
const searchForm = reactive<SearchForm>({
  username: '',
  bookTitle: '',
  status: undefined,
  dateRange: undefined
});

// 借阅表单数据
const formData = reactive<BorrowForm>({
  userId: undefined,
  bookId: undefined,
  borrowDate: undefined,
  dueDate: undefined,
  remark: ''
});

// 当前查看的记录
const currentRecord = ref<BorrowRecord | null>(null);

// 用户数据（从API获取）
const users = ref<User[]>([]);

// 可借图书数据（从API获取）
const availableBooks = ref<Book[]>([]);

// 获取用户列表（用于借阅表单）
const fetchUsers = async () => {
  try {
    // 这里可以调用用户API获取用户列表
    // 暂时使用模拟数据
    users.value = [
      { id: 1, username: 'zhangsan', realName: '张三' },
      { id: 2, username: 'lisi', realName: '李四' },
      { id: 3, username: 'wangwu', realName: '王五' },
      { id: 4, username: 'zhaoliu', realName: '赵六' }
    ];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 获取可借图书列表（用于借阅表单）
const fetchAvailableBooks = async () => {
  try {
    // 这里可以调用图书API获取可借图书列表
    // 暂时使用模拟数据
    availableBooks.value = [
      { id: 1, title: '红楼梦', author: '曹雪芹', totalQuantity: 15, availableQuantity: 10 },
      { id: 2, title: 'Java核心技术', author: 'Cay S. Horstmann', totalQuantity: 8, availableQuantity: 5 },
      { id: 3, title: '平凡的世界', author: '路遥', totalQuantity: 3, availableQuantity: 1 },
      { id: 4, title: '史记', author: '司马迁', totalQuantity: 12, availableQuantity: 8 }
    ];
  } catch (error) {
    console.error('获取图书列表失败:', error);
  }
};

// 借阅记录数据
const borrowRecords = ref<BorrowRecord[]>([]);
const borrowPageData = ref<PageResult<BorrowRecord>>({
  records: [],
  total: 0,
  current: 1,
  size: 10,
  pages: 0
});

// 获取借阅记录列表
const fetchBorrowList = async () => {
  try {
    loading.value = true;

    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status || undefined,
      userId: searchForm.userId || undefined,
      bookId: searchForm.bookId || undefined
    };

    // 尝试调用真实API
    const response = await getBorrowList(params);

    if (response.code === 200) {
      borrowPageData.value = response.data;
      borrowRecords.value = response.data.records;
      pagination.total = response.data.total;
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    console.warn('获取借阅记录失败，使用模拟数据:', error.message);
    // 使用模拟数据
    borrowPageData.value = mockBorrowList;
    borrowRecords.value = mockBorrowList.records;
    pagination.total = mockBorrowList.total;
  } finally {
    loading.value = false;
  }
};

// 原模拟借阅记录数据（作为备用）
const mockBorrowRecords = ref<BorrowRecord[]>([
  {
    id: 1,
    userId: 1,
    username: 'zhangsan',
    realName: '张三',
    bookId: 1,
    bookTitle: '红楼梦',
    bookAuthor: '曹雪芹',
    borrowDate: '2024-01-10',
    dueDate: '2024-02-10',
    status: 'BORROWED',
    remark: '正常借阅'
  },
  {
    id: 2,
    userId: 2,
    username: 'lisi',
    realName: '李四',
    bookId: 2,
    bookTitle: 'Java核心技术',
    bookAuthor: 'Cay S. Horstmann',
    borrowDate: '2024-01-05',
    dueDate: '2024-02-05',
    returnDate: '2024-01-25',
    status: 'RETURNED',
    remark: '提前归还'
  },
  {
    id: 3,
    userId: 3,
    username: 'wangwu',
    realName: '王五',
    bookId: 3,
    bookTitle: '平凡的世界',
    bookAuthor: '路遥',
    borrowDate: '2023-12-15',
    dueDate: '2024-01-15',
    status: 'OVERDUE',
    remark: '已逾期'
  },
  {
    id: 4,
    userId: 4,
    username: 'zhaoliu',
    realName: '赵六',
    bookId: 4,
    bookTitle: '史记',
    bookAuthor: '司马迁',
    borrowDate: '2024-01-12',
    dueDate: '2024-02-12',
    status: 'BORROWED'
  }
]);

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '用户头像',
    key: 'userAvatar',
    width: 80,
    align: 'center'
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    key: 'realName',
    width: 120
  },
  {
    title: '图书封面',
    key: 'bookCover',
    width: 80,
    align: 'center'
  },
  {
    title: '图书名称',
    dataIndex: 'bookTitle',
    key: 'bookTitle',
    width: 200
  },
  {
    title: '作者',
    dataIndex: 'bookAuthor',
    key: 'bookAuthor',
    width: 150
  },
  {
    title: '借阅时间',
    key: 'borrowDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      console.log('🕐 管理员页面渲染借阅时间:', record.borrowDate);
      if (!record.borrowDate) {
        console.warn('⚠️ 借阅时间为空:', record);
        return '-';
      }
      return dayjs(record.borrowDate).format('YYYY-MM-DD HH:mm');
    }
  },
  {
    title: '归还时间',
    key: 'returnDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      console.log('🕐 管理员页面渲染归还时间:', record.returnDate);
      if (!record.returnDate) {
        return record.status === 'RETURNED' ? '未记录' : '未归还';
      }
      return dayjs(record.returnDate).format('YYYY-MM-DD HH:mm');
    }
  },
  {
    title: '剩余天数',
    key: 'daysLeft',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: BorrowRecord) => ({
    disabled: record.status !== 'BORROWED'
  })
};

// 表单验证规则
const formRules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  bookId: [
    { required: true, message: '请选择图书', trigger: 'change' }
  ],
  borrowDate: [
    { required: true, message: '请选择借阅日期', trigger: 'change' }
  ],
  dueDate: [
    { required: true, message: '请选择应还日期', trigger: 'change' }
  ]
};

// 计算属性：过滤后的借阅记录
const filteredBorrows = computed(() => {
  let result = borrowRecords.value;

  if (searchForm.username) {
    result = result.filter(record =>
      record.username.toLowerCase().includes(searchForm.username!.toLowerCase()) ||
      record.realName.toLowerCase().includes(searchForm.username!.toLowerCase())
    );
  }

  if (searchForm.bookTitle) {
    result = result.filter(record =>
      record.bookTitle.toLowerCase().includes(searchForm.bookTitle!.toLowerCase())
    );
  }

  if (searchForm.status) {
    result = result.filter(record => record.status === searchForm.status);
  }

  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    const [start, end] = searchForm.dateRange;
    result = result.filter(record => {
      const borrowDate = dayjs(record.borrowDate);
      return borrowDate.isAfter(start.subtract(1, 'day')) && borrowDate.isBefore(end.add(1, 'day'));
    });
  }

  // 更新分页总数
  pagination.total = result.length;

  // 分页处理
  const startIndex = (pagination.current - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;

  return result.slice(startIndex, endIndex);
});

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'BORROWED': return 'blue';
    case 'RETURNED': return 'green';
    case 'OVERDUE': return 'red';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'BORROWED': return '借阅中';
    case 'RETURNED': return '已归还';
    case 'OVERDUE': return '逾期';
    default: return '未知';
  }
};

// 获取剩余天数
const getDaysLeft = (record: BorrowRecord) => {
  if (record.status === 'RETURNED') {
    return '已归还';
  }

  const today = dayjs();
  const dueDate = dayjs(record.dueDate);
  const diff = dueDate.diff(today, 'day');

  if (diff > 0) {
    return `${diff}天`;
  } else if (diff === 0) {
    return '今日到期';
  } else {
    return `逾期${Math.abs(diff)}天`;
  }
};

// 获取剩余天数样式类
const getDaysLeftClass = (record: BorrowRecord) => {
  if (record.status === 'RETURNED') {
    return 'returned';
  }

  const today = dayjs();
  const dueDate = dayjs(record.dueDate);
  const diff = dueDate.diff(today, 'day');

  if (diff < 0) {
    return 'overdue';
  } else if (diff <= 3) {
    return 'warning';
  } else {
    return 'normal';
  }
};

// 用户筛选函数
const filterUserOption = (input: string, option: any) => {
  const user = users.value.find(u => u.id === option.value);
  if (!user) return false;
  return user.username.toLowerCase().includes(input.toLowerCase()) ||
         user.realName.toLowerCase().includes(input.toLowerCase());
};

// 图书筛选函数
const filterBookOption = (input: string, option: any) => {
  const book = availableBooks.value.find(b => b.id === option.value);
  if (!book) return false;
  return book.title.toLowerCase().includes(input.toLowerCase()) ||
         book.author.toLowerCase().includes(input.toLowerCase());
};

// 这些函数已在后面重新定义，删除重复声明

// 显示添加模态框
const showAddModal = () => {
  modalVisible.value = true;
  resetForm();
};

// 显示详情模态框
const showDetailModal = (record: BorrowRecord) => {
  currentRecord.value = record;
  detailModalVisible.value = true;
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    userId: undefined,
    bookId: undefined,
    borrowDate: undefined,
    dueDate: undefined,
    remark: ''
  });
  formRef.value?.resetFields();
};

// 模态框确定处理
const handleModalOk = async () => {
  try {
    await formRef.value?.validate();
    modalLoading.value = true;

    console.log('创建借阅记录:', formData);

    // 调用真实API
    await createBorrow({
      userId: formData.userId!,
      bookId: formData.bookId!,
      borrowDate: formData.borrowDate!.format('YYYY-MM-DD'),
      dueDate: formData.dueDate!.format('YYYY-MM-DD'),
      remark: formData.remark
    });

    message.success('借阅记录创建成功');

    // 重新获取借阅记录列表
    await fetchBorrowList();

    modalVisible.value = false;
    resetForm();
  } catch (error: any) {
    console.error('创建借阅记录失败:', error);
    message.error(error.response?.data?.message || error.message || '创建失败');
  } finally {
    modalLoading.value = false;
  }
};

// 模态框取消处理
const handleModalCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 归还图书
const returnBookAction = async (record: BorrowRecord) => {
  try {
    loading.value = true;

    console.log('归还图书:', { recordId: record.id, bookTitle: record.bookTitle });

    // 调用真实API
    await returnBook(record.id, '正常归还');

    // 重新获取借阅记录列表
    await fetchBorrowList();

    message.success('图书归还成功');
  } catch (error: any) {
    console.error('归还图书失败:', error);
    message.error(error.response?.data?.message || error.message || '归还失败');
  } finally {
    loading.value = false;
  }
};

// 批量归还
const handleBatchReturn = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要归还的记录');
    return;
  }

  try {
    loading.value = true;

    console.log('批量归还图书:', { recordIds: selectedRowKeys.value });

    // 调用真实API（如果后端支持批量归还）
    // 这里暂时使用循环调用单个归还API
    for (const recordId of selectedRowKeys.value) {
      await returnBook(recordId as number, '批量归还');
    }

    selectedRowKeys.value = [];

    // 重新获取借阅记录列表
    await fetchBorrowList();

    message.success('批量归还成功');
  } catch (error: any) {
    console.error('批量归还失败:', error);
    message.error(error.response?.data?.message || error.message || '批量归还失败');
  } finally {
    loading.value = false;
  }
};

// 处理逾期
const handleOverdue = async (record: BorrowRecord) => {
  try {
    loading.value = true;

    console.log('处理逾期记录:', { recordId: record.id, bookTitle: record.bookTitle });

    // 这里可以调用逾期处理API，比如发送通知、计算罚金等
    // 暂时使用模拟处理，实际应该调用后端API
    message.success('逾期处理完成（功能待完善）');
  } catch (error: any) {
    console.error('逾期处理失败:', error);
    message.error(error.response?.data?.message || error.message || '处理失败');
  } finally {
    loading.value = false;
  }
};

// 删除借阅记录
const deleteBorrow = async (recordId: number) => {
  try {
    loading.value = true;

    console.log('删除借阅记录:', { recordId });

    // 调用真实API（如果后端支持删除借阅记录）
    // 注意：通常借阅记录不应该被删除，而是标记为已删除
    // 这里暂时提示功能待实现
    message.warning('删除借阅记录功能待后端实现');

    // 如果后端支持，可以这样调用：
    // await deleteBorrowRecord(recordId);
    // await fetchBorrowList();
    // message.success('借阅记录删除成功');
  } catch (error: any) {
    console.error('删除借阅记录失败:', error);
    message.error(error.response?.data?.message || error.message || '删除失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchBorrowList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    userId: undefined,
    bookId: undefined
  });
  pagination.current = 1;
  fetchBorrowList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchBorrowList();
};

// 初始化
onMounted(() => {
  fetchUsers();
  fetchAvailableBooks();
  fetchBorrowList();
});
</script>

<style scoped>
.borrow-management {
  background: transparent;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.page-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-header {
  margin-bottom: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.header-text h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.header-text p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

.search-card {
  margin-bottom: 12px;
}

.search-form {
  margin-bottom: 0;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.ant-form-item:last-child) {
  margin-right: 0;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.borrow-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 剩余天数样式 */
.normal {
  color: #52c41a;
  font-weight: 500;
}

.warning {
  color: #faad14;
  font-weight: 600;
}

.overdue {
  color: #ff4d4f;
  font-weight: 600;
}

.returned {
  color: #8c8c8c;
  font-style: italic;
}

/* 主题色按钮样式 */
.borrow-management :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.borrow-management :deep(.ant-btn-primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 表格样式优化 */
.borrow-management :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #1f2937;
}

.borrow-management :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8faff;
}

/* 标签样式 */
.borrow-management :deep(.ant-tag) {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 模态框样式 */
.borrow-management :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.borrow-management :deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.borrow-management :deep(.ant-modal-body) {
  padding: 24px;
}

/* 描述列表样式 */
.borrow-management :deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #1f2937;
}

/* 选择器样式 */
.borrow-management :deep(.ant-select),
.borrow-management :deep(.ant-picker) {
  border-radius: 6px;
}

/* 桌面端大屏优化 */
@media (min-width: 1200px) {
  .page-header {
    margin-bottom: 20px;
  }

  .search-card {
    margin-bottom: 16px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .borrow-management {
    padding: 0;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    margin-top: 12px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form :deep(.ant-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-form :deep(.ant-form-item:last-child) {
    margin-bottom: 0;
  }

  .borrow-management :deep(.ant-table) {
    font-size: 14px;
  }

  .borrow-management :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .borrow-management :deep(.ant-space) {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .borrow-management {
    padding: 0;
  }

  .header-text h1 {
    font-size: 20px;
  }

  .borrow-management :deep(.ant-btn) {
    font-size: 14px;
    height: 36px;
    padding: 0 12px;
  }

  .borrow-management :deep(.ant-descriptions) {
    font-size: 14px;
  }
}
</style>
