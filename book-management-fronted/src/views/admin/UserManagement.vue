<template>
  <div class="user-management">
    <!-- 页面标题和操作区 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>用户管理</h1>
          <p>管理系统中的所有用户信息</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" @click="showAddModal">
            <template #icon>
              <PlusOutlined />
            </template>
            添加用户
          </a-button>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.username"
            placeholder="请输入用户名"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.email"
            placeholder="请输入邮箱"
            allow-clear
          >
            <template #prefix>
              <MailOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 100%"
          >
            <a-select-option value="1">启用</a-select-option>
            <a-select-option value="0">禁用</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 用户列表 -->
    <a-card class="table-card" :bordered="false">

    <!-- 搜索和筛选区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="用户名">
          <a-input
            v-model:value="searchForm.username"
            placeholder="请输入用户名"
            allow-clear
            @change="handleSearch"
          />
        </a-form-item>
        <a-form-item label="邮箱">
          <a-input
            v-model:value="searchForm.email"
            placeholder="请输入邮箱"
            allow-clear
            @change="handleSearch"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
            @change="handleSearch"
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="角色">
          <a-select
            v-model:value="searchForm.role"
            placeholder="请选择角色"
            allow-clear
            style="width: 120px"
            @change="handleSearch"
          >
            <a-select-option value="ADMIN">管理员</a-select-option>
            <a-select-option value="USER">普通用户</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 用户列表表格 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="filteredUsers"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: 800 }"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :size="40">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'role'">
            <a-tag :color="record.role === 'ADMIN' ? 'purple' : 'blue'">
              {{ record.role === 'ADMIN' ? '管理员' : '普通用户' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="showEditModal(record)">
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                :danger="record.status === 1"
                @click="toggleUserStatus(record)"
              >
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>
              <a-popconfirm
                title="确定要删除这个用户吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteUser(record.id)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑用户模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑用户' : '添加用户'"
      :confirm-loading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      width="600px"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="user-form"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="用户名" name="username">
              <a-input v-model:value="formData.username" placeholder="请输入用户名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="真实姓名" name="realName">
              <a-input v-model:value="formData.realName" placeholder="请输入真实姓名" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="邮箱" name="email">
              <a-input v-model:value="formData.email" placeholder="请输入邮箱" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16" v-if="!isEdit">
          <a-col :span="12">
            <a-form-item label="密码" name="password">
              <a-input-password v-model:value="formData.password" placeholder="请输入密码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="确认密码" name="confirmPassword">
              <a-input-password v-model:value="formData.confirmPassword" placeholder="请再次输入密码" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="角色" name="role">
              <a-select v-model:value="formData.role" placeholder="请选择角色">
                <a-select-option value="USER">普通用户</a-select-option>
                <a-select-option value="ADMIN">管理员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance, TableColumnsType } from 'ant-design-vue';
import {
  PlusOutlined,
  UserOutlined,
  SearchOutlined,
  MailOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { getUserList, createUser, updateUser, deleteUser as deleteUserApi, toggleUserStatus as toggleUserStatusApi, resetUserPassword } from '@/api/user';
import { mockUserList } from '@/api/mock';
import type { User, PageResult } from '@/types';
import { useUserStore } from '@/stores/user';

// 使用全局类型定义
const userStore = useUserStore();

// 表单数据类型
interface UserForm {
  username: string;
  realName: string;
  email: string;
  phone?: string;
  password?: string;
  confirmPassword?: string;
  role: 'SUPER_ADMIN' | 'ADMIN' | 'USER';
  status: number; // 0-禁用，1-启用
}

// 搜索表单类型
interface SearchForm {
  username?: string;
  email?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  role?: 'ADMIN' | 'USER';
}

// 响应式数据
const loading = ref(false);
const modalVisible = ref(false);
const modalLoading = ref(false);
const isEdit = ref(false);
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive<SearchForm>({
  username: '',
  email: '',
  status: undefined,
  role: undefined
});

// 用户表单数据
const formData = reactive<UserForm>({
  username: '',
  realName: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  role: 'USER',
  status: 1 // 1-启用
});

// 用户数据
const users = ref<User[]>([]);
const userPageData = ref<PageResult<User>>({
  records: [],
  total: 0,
  current: 1,
  size: 10,
  pages: 0
});

// 当前编辑的用户ID
const currentUserId = ref<number | null>(null);

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '头像',
    key: 'avatar',
    width: 80,
    align: 'center'
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120
  },
  {
    title: '真实姓名',
    dataIndex: 'realName',
    key: 'realName',
    width: 120
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 130
  },
  {
    title: '角色',
    key: 'role',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '最后登录',
    dataIndex: 'lastLoginTime',
    key: 'lastLoginTime',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { max: 50, message: '真实姓名不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && value !== formData.password) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 获取用户列表数据
const fetchUserList = async () => {
  try {
    loading.value = true;

    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      keyword: searchForm.username || searchForm.email || undefined,
      role: searchForm.role || undefined,
      status: searchForm.status
    };

    // 尝试调用真实API
    const response = await getUserList(params);

    if (response.code === 200) {
      userPageData.value = response.data;
      users.value = response.data.records;
      pagination.total = response.data.total;
    } else {
      throw new Error(response.message);
    }
  } catch (error: any) {
    console.warn('获取用户列表失败，使用模拟数据:', error.message);
    // 使用模拟数据
    userPageData.value = mockUserList;
    users.value = mockUserList.records;
    pagination.total = mockUserList.total;
  } finally {
    loading.value = false;
  }
};

// 计算属性：过滤后的用户列表
const filteredUsers = computed(() => {
  return users.value;
});

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  fetchUserList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    email: '',
    status: undefined,
    role: undefined
  });
  pagination.current = 1;
  fetchUserList();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchUserList();
};

// 显示添加模态框
const showAddModal = () => {
  isEdit.value = false;
  modalVisible.value = true;
  resetForm();
};

// 显示编辑模态框
const showEditModal = (record: User) => {
  isEdit.value = true;
  currentUserId.value = record.id;
  modalVisible.value = true;

  // 填充表单数据
  Object.assign(formData, {
    username: record.username,
    realName: record.realName,
    email: record.email,
    phone: record.phone || '',
    role: record.role,
    status: record.status,
    password: '',
    confirmPassword: ''
  });
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    username: '',
    realName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'USER',
    status: 'ACTIVE'
  });
  formRef.value?.resetFields();
};

// 模态框确定处理
const handleModalOk = async () => {
  try {
    await formRef.value?.validate();
    modalLoading.value = true;

    if (isEdit.value) {
      // 编辑用户
      console.log('编辑用户:', { userId: currentUserId.value, formData });

      await updateUser(currentUserId.value!, {
        email: formData.email,
        realName: formData.realName,
        phone: formData.phone,
        role: formData.role
      });

      message.success('用户信息更新成功');
    } else {
      // 新增用户
      console.log('创建用户:', formData);

      await createUser({
        username: formData.username,
        password: formData.password!,
        email: formData.email,
        realName: formData.realName,
        phone: formData.phone,
        role: formData.role
      });

      message.success('用户创建成功');
    }

    // 重新获取用户列表
    await fetchUserList();

    modalVisible.value = false;
    resetForm();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    modalLoading.value = false;
  }
};

// 模态框取消处理
const handleModalCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 切换用户状态
const toggleUserStatus = async (record: User) => {
  try {
    loading.value = true;

    console.log('开始切换用户状态:', {
      userId: record.id,
      currentStatus: record.status,
      userName: record.username
    });

    // 调用真实API
    const response = await toggleUserStatusApi(record.id);
    console.log('API响应:', response);

    // 重新获取用户列表以确保数据同步
    await fetchUserList();

    const statusText = record.status === 1 ? '禁用' : '启用';
    message.success(`用户已${statusText}`);
  } catch (error: any) {
    console.error('切换用户状态失败:', error);
    console.error('错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    message.error(error.response?.data?.message || error.message || '操作失败');
  } finally {
    loading.value = false;
  }
};

// 删除用户
const deleteUser = async (userId: number) => {
  try {
    loading.value = true;

    console.log('删除用户:', { userId });

    // 调用真实API
    await deleteUserApi(userId);

    // 重新获取用户列表
    await fetchUserList();

    message.success('用户删除成功');
  } catch (error: any) {
    console.error('删除用户失败:', error);
    message.error(error.response?.data?.message || error.message || '删除失败');
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  // 初始化用户信息
  userStore.initUserInfo();

  console.log('当前用户信息:', {
    userInfo: userStore.userInfo,
    isLoggedIn: userStore.isLoggedIn,
    isSuperAdmin: userStore.isSuperAdmin,
    token: userStore.token ? '已设置' : '未设置'
  });

  fetchUserList();
});
</script>

<style scoped>
.user-management {
  background: transparent;
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-text h1 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.header-text p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  flex-shrink: 0;
}

.search-card {
  margin-bottom: 16px;
}

.search-form {
  margin-bottom: 0;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.ant-form-item:last-child) {
  margin-right: 0;
}

.table-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}



.user-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

/* 主题色按钮样式 */
.user-management :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.user-management :deep(.ant-btn-primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 表格样式优化 */
.user-management :deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #1f2937;
}

.user-management :deep(.ant-table-tbody > tr:hover > td) {
  background: #f8faff;
}

/* 标签样式 */
.user-management :deep(.ant-tag) {
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 模态框样式 */
.user-management :deep(.ant-modal-header) {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.user-management :deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.user-management :deep(.ant-modal-body) {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    margin-top: 16px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form :deep(.ant-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-form :deep(.ant-form-item:last-child) {
    margin-bottom: 0;
  }

  .user-management :deep(.ant-table) {
    font-size: 14px;
  }

  .user-management :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

@media (max-width: 480px) {
  .user-management {
    padding: 12px;
  }

  .header-text h1 {
    font-size: 20px;
  }

  .user-management :deep(.ant-btn) {
    font-size: 14px;
    height: 36px;
    padding: 0 12px;
  }
}
</style>
