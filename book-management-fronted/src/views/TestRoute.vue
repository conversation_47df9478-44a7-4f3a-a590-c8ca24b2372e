<template>
  <div class="test-route">
    <h1>路由测试页面</h1>
    <p>如果你能看到这个页面，说明路由工作正常！</p>
    <p>当前时间：{{ currentTime }}</p>
    <a-button type="primary" @click="goToLogin">去登录页</a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const currentTime = ref('');

const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

const goToLogin = () => {
  router.push('/login');
};

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);
});
</script>

<style scoped>
.test-route {
  padding: 40px;
  text-align: center;
  background: white;
  border-radius: 8px;
  margin: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-route h1 {
  color: #667eea;
  margin-bottom: 20px;
}

.test-route p {
  font-size: 16px;
  margin-bottom: 20px;
}
</style>
