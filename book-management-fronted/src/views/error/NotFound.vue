<template>
  <div class="not-found">
    <div class="not-found-content">
      <div class="not-found-image">
        <img src="/404.svg" alt="404" />
      </div>
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被删除。</p>
      <a-button type="primary" size="large" @click="goHome">
        返回首页
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/dashboard');
};
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
}

.not-found-image {
  margin-bottom: 32px;
}

.not-found-image img {
  width: 200px;
  height: auto;
}

.not-found h1 {
  font-size: 72px;
  font-weight: 700;
  color: #1890ff;
  margin-bottom: 16px;
}

.not-found h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.not-found p {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 32px;
}
</style>
