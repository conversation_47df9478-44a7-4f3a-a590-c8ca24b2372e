<template>
  <div class="api-test">
    <h2>API测试页面</h2>
    
    <a-card title="借阅记录API测试" style="margin-bottom: 20px;">
      <a-space direction="vertical" style="width: 100%;">
        <a-button type="primary" @click="testGetCurrentBorrows">
          测试获取当前借阅记录
        </a-button>
        
        <a-button type="default" @click="testGetAllBorrows">
          测试获取所有借阅记录
        </a-button>
        
        <a-button type="default" @click="testDirectAPI">
          直接调用API
        </a-button>
      </a-space>
    </a-card>

    <a-card title="API响应结果" v-if="apiResponse">
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 400px; overflow-y: auto;">{{ JSON.stringify(apiResponse, null, 2) }}</pre>
    </a-card>

    <a-card title="错误信息" v-if="errorMessage">
      <a-alert :message="errorMessage" type="error" />
    </a-card>

    <a-card title="控制台日志" v-if="consoleLogs.length > 0">
      <div style="max-height: 300px; overflow-y: auto;">
        <div v-for="(log, index) in consoleLogs" :key="index" 
             style="padding: 4px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px;">
          <span :style="{ color: getLogColor(log.level) }">
            [{{ log.timestamp }}] {{ log.level.toUpperCase() }}: {{ log.message }}
          </span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getCurrentBorrows, getUserBorrowRecords } from '@/api/userBorrow';
import request from '@/utils/request';

const apiResponse = ref<any>(null);
const errorMessage = ref<string>('');
const consoleLogs = ref<Array<{level: string, message: string, timestamp: string}>>([]);

// 拦截console.log
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.log = (...args) => {
  consoleLogs.value.push({
    level: 'log',
    message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '),
    timestamp: new Date().toLocaleTimeString()
  });
  originalConsoleLog(...args);
};

console.error = (...args) => {
  consoleLogs.value.push({
    level: 'error',
    message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '),
    timestamp: new Date().toLocaleTimeString()
  });
  originalConsoleError(...args);
};

console.warn = (...args) => {
  consoleLogs.value.push({
    level: 'warn',
    message: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' '),
    timestamp: new Date().toLocaleTimeString()
  });
  originalConsoleWarn(...args);
};

const getLogColor = (level: string) => {
  const colors: Record<string, string> = {
    'log': '#333',
    'error': '#ff4d4f',
    'warn': '#fa8c16'
  };
  return colors[level] || '#333';
};

const clearResults = () => {
  apiResponse.value = null;
  errorMessage.value = '';
  consoleLogs.value = [];
};

const testGetCurrentBorrows = async () => {
  clearResults();
  try {
    console.log('🧪 开始测试getCurrentBorrows...');
    const result = await getCurrentBorrows();
    console.log('✅ getCurrentBorrows成功:', result);
    apiResponse.value = result;
  } catch (error) {
    console.error('❌ getCurrentBorrows失败:', error);
    errorMessage.value = `getCurrentBorrows失败: ${error}`;
  }
};

const testGetAllBorrows = async () => {
  clearResults();
  try {
    console.log('🧪 开始测试getUserBorrowRecords...');
    const result = await getUserBorrowRecords({
      current: 1,
      size: 10
    });
    console.log('✅ getUserBorrowRecords成功:', result);
    apiResponse.value = result;
  } catch (error) {
    console.error('❌ getUserBorrowRecords失败:', error);
    errorMessage.value = `getUserBorrowRecords失败: ${error}`;
  }
};

const testDirectAPI = async () => {
  clearResults();
  try {
    console.log('🧪 开始直接调用API...');
    const result = await request.get('/borrow/records', { 
      params: { current: 1, size: 10 } 
    });
    console.log('✅ 直接API调用成功:', result);
    apiResponse.value = result;
  } catch (error) {
    console.error('❌ 直接API调用失败:', error);
    errorMessage.value = `直接API调用失败: ${error}`;
  }
};
</script>

<style scoped>
.api-test {
  padding: 20px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
