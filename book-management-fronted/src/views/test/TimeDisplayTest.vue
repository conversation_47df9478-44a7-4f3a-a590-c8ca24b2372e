<template>
  <div class="time-display-test">
    <h2>时间显示功能测试</h2>
    
    <a-card title="时间格式化测试" style="margin-bottom: 20px;">
      <div class="test-section">
        <h3>当前时间：{{ new Date().toISOString() }}</h3>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <h4>友好格式</h4>
            <p>{{ formatFriendlyDate(new Date().toISOString()) }}</p>
          </a-col>
          <a-col :span="8">
            <h4>标准格式</h4>
            <p>{{ formatDate(new Date().toISOString()) }}</p>
          </a-col>
          <a-col :span="8">
            <h4>包含时间</h4>
            <p>{{ formatDate(new Date().toISOString(), true) }}</p>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <a-card title="剩余天数测试" style="margin-bottom: 20px;">
      <div class="test-section">
        <a-row :gutter="16">
          <a-col :span="6" v-for="(test, index) in timeTests" :key="index">
            <div class="time-test-item">
              <h4>{{ test.label }}</h4>
              <p>日期：{{ formatDate(test.date) }}</p>
              <p :class="getRemainingDaysInfo(test.date).color">
                {{ getRemainingDaysInfo(test.date).text }}
              </p>
              <a-tag :color="getUrgencyColor(getRemainingDaysInfo(test.date).urgency)">
                {{ getRemainingDaysInfo(test.date).status }}
              </a-tag>
            </div>
          </a-col>
        </a-row>
      </div>
    </a-card>

    <a-card title="模拟借阅记录">
      <a-table 
        :columns="testColumns" 
        :data-source="mockBorrowRecords" 
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'remainingDays'">
            <div class="remaining-days-cell">
              <span :class="getRemainingDaysInfo(record.dueDate).color">
                {{ getRemainingDaysInfo(record.dueDate).text }}
              </span>
              <div v-if="getRemainingDaysInfo(record.dueDate).urgency === 'critical'" 
                   class="urgency-indicator critical">
                🚨
              </div>
              <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'high'" 
                   class="urgency-indicator high">
                ⚠️
              </div>
              <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'medium'" 
                   class="urgency-indicator medium">
                ⏰
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 时间格式化函数
const formatDate = (dateString: string, includeTime: boolean = false) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    if (includeTime) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

const formatFriendlyDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('友好日期格式化错误:', error);
    return '-';
  }
};

const calculateRemainingDays = (dueDate: string) => {
  if (!dueDate) return 0;
  try {
    const due = new Date(dueDate);
    const now = new Date();
    if (isNaN(due.getTime())) return 0;

    const dueDay = new Date(due.getFullYear(), due.getMonth(), due.getDate());
    const nowDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const diffTime = dueDay.getTime() - nowDay.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  } catch (error) {
    console.error('计算剩余天数时出错:', error);
    return 0;
  }
};

const getRemainingDaysInfo = (dueDate: string) => {
  const remainingDays = calculateRemainingDays(dueDate);
  
  if (remainingDays < 0) {
    return {
      text: `逾期 ${Math.abs(remainingDays)} 天`,
      color: 'text-red',
      status: 'overdue',
      urgency: 'critical'
    };
  } else if (remainingDays === 0) {
    return {
      text: '今日到期',
      color: 'text-red',
      status: 'due-today',
      urgency: 'critical'
    };
  } else if (remainingDays === 1) {
    return {
      text: '明日到期',
      color: 'text-orange',
      status: 'due-tomorrow',
      urgency: 'high'
    };
  } else if (remainingDays <= 3) {
    return {
      text: `${remainingDays} 天后到期`,
      color: 'text-orange',
      status: 'due-soon',
      urgency: 'medium'
    };
  } else if (remainingDays <= 7) {
    return {
      text: `${remainingDays} 天`,
      color: 'text-yellow',
      status: 'normal',
      urgency: 'low'
    };
  } else {
    return {
      text: `${remainingDays} 天`,
      color: 'text-green',
      status: 'safe',
      urgency: 'none'
    };
  }
};

const getUrgencyColor = (urgency: string) => {
  const colorMap: Record<string, string> = {
    'critical': 'red',
    'high': 'orange',
    'medium': 'gold',
    'low': 'blue',
    'none': 'green'
  };
  return colorMap[urgency] || 'default';
};

// 测试数据
const now = new Date();
const timeTests = ref([
  {
    label: '逾期2天',
    date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    label: '今日到期',
    date: new Date(now.getTime()).toISOString()
  },
  {
    label: '明日到期',
    date: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    label: '3天后到期',
    date: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    label: '7天后到期',
    date: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    label: '15天后到期',
    date: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString()
  }
]);

const testColumns = [
  { title: '图书名称', dataIndex: 'title', key: 'title' },
  { title: '借阅时间', dataIndex: 'borrowDate', key: 'borrowDate', 
    customRender: ({ record }: any) => formatFriendlyDate(record.borrowDate) },
  { title: '归还截止', dataIndex: 'dueDate', key: 'dueDate',
    customRender: ({ record }: any) => formatFriendlyDate(record.dueDate) },
  { title: '剩余天数', key: 'remainingDays' }
];

const mockBorrowRecords = ref([
  {
    id: 1,
    title: 'Java编程思想',
    borrowDate: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    dueDate: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 2,
    title: 'Spring Boot实战',
    borrowDate: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000).toISOString(),
    dueDate: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 3,
    title: 'Vue.js设计与实现',
    borrowDate: new Date(now.getTime() - 32 * 24 * 60 * 60 * 1000).toISOString(),
    dueDate: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString()
  }
]);
</script>

<style scoped>
.time-display-test {
  padding: 20px;
}

.test-section {
  margin: 16px 0;
}

.time-test-item {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  margin-bottom: 16px;
}

.text-red { color: #ff4d4f; font-weight: 600; }
.text-orange { color: #fa8c16; font-weight: 600; }
.text-yellow { color: #faad14; font-weight: 600; }
.text-green { color: #52c41a; font-weight: 600; }

.remaining-days-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.urgency-indicator {
  font-size: 16px;
  line-height: 1;
}

.urgency-indicator.critical {
  animation: blink 1s infinite;
}

.urgency-indicator.high {
  animation: pulse 2s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
</style>
