<template>
  <div class="user-book-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>图书浏览</h1>
        <p>浏览和搜索图书馆中的所有图书</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ pagination.total }}</span>
          <span class="stat-label">总图书</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ availableCount }}</span>
          <span class="stat-label">可借阅</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card class="search-card" :bordered="false">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="24" :md="12" :lg="10" :xl="8">
            <div class="search-input-wrapper">
              <a-input-search
                v-model:value="searchForm.keyword"
                placeholder="搜索图书名称、作者或ISBN"
                enter-button="搜索"
                size="large"
                :loading="searchLoading"
                @search="handleSearch"
                class="search-input"
              />
            </div>
          </a-col>
          <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="4">
            <div class="filter-wrapper">
              <a-select
                v-model:value="searchForm.category"
                placeholder="全部分类"
                size="large"
                style="width: 100%"
                @change="handleSearch"
                class="filter-select"
                :get-popup-container="(trigger) => trigger.parentNode"
              >
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option
                  v-for="category in categories"
                  :key="category"
                  :value="category"
                >
                  {{ category }}
                </a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :xs="12" :sm="8" :md="6" :lg="4" :xl="4">
            <div class="filter-wrapper">
              <a-select
                v-model:value="searchForm.status"
                placeholder="全部状态"
                size="large"
                style="width: 100%"
                @change="handleSearch"
                class="filter-select"
                :get-popup-container="(trigger) => trigger.parentNode"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="AVAILABLE">可借阅</a-select-option>
                <a-select-option value="BORROWED">已借出</a-select-option>
              </a-select>
            </div>
          </a-col>
          <a-col :xs="24" :sm="8" :md="6" :lg="6" :xl="8">
            <div class="search-actions">
              <a-button
                type="primary"
                size="large"
                @click="resetSearch"
                class="reset-btn"
              >
                重置
              </a-button>
              <a-button
                size="large"
                @click="forceRefresh"
                :loading="loading"
                class="refresh-btn"
              >
                🔄 刷新
              </a-button>
              <a-button
                size="large"
                @click="toggleViewMode"
                class="view-toggle-btn"
                :title="viewMode === 'grid' ? '切换到列表视图' : '切换到网格视图'"
              >
                <template #icon>
                  <AppstoreOutlined v-if="viewMode === 'list'" />
                  <UnorderedListOutlined v-else />
                </template>
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 图书列表 -->
    <div class="book-list-section">
      <a-card class="book-list-card" :bordered="false">
        <!-- 列表头部信息 -->
        <div class="list-header">
          <div class="list-info">
            <span class="result-count">
              找到 <strong>{{ pagination.total }}</strong> 本图书
            </span>
            <span v-if="searchForm.keyword" class="search-keyword">
              关键词: "{{ searchForm.keyword }}"
            </span>
          </div>
          <div class="list-actions">
            <a-button-group>
              <a-button
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="setViewMode('grid')"
                size="small"
              >
                <template #icon><AppstoreOutlined /></template>
                网格
              </a-button>
              <a-button
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="setViewMode('list')"
                size="small"
              >
                <template #icon><UnorderedListOutlined /></template>
                列表
              </a-button>
            </a-button-group>
          </div>
        </div>

        <!-- 加载状态 -->
        <a-spin :spinning="loading" size="large">
          <!-- 网格视图 -->
          <div v-if="viewMode === 'grid'" class="grid-view">
            <a-row :gutter="[20, 20]">
              <a-col
                v-for="book in books"
                :key="book.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
                :xxl="4"
              >
                <div class="book-card-wrapper">
                  <a-card
                    class="book-card"
                    :hoverable="true"
                    :loading="book.loading"
                  >
                    <!-- 图书封面 -->
                    <div class="book-cover" @click="viewBookDetail(book)">
                      <img
                        :src="getBookCover(book)"
                        :alt="book.title"
                        class="cover-image"
                        @error="handleImageError"
                        loading="lazy"
                      />
                      <div class="cover-overlay">
                        <EyeOutlined class="view-icon" />
                      </div>
                    </div>

                    <!-- 图书信息 -->
                    <div class="book-content">
                      <h3 class="book-title" @click="viewBookDetail(book)">
                        {{ book.title }}
                      </h3>
                      <p class="book-author">{{ book.author }}</p>

                      <div class="book-meta">
                        <a-tag
                          :color="getCategoryColor(book.category)"
                          class="category-tag"
                        >
                          {{ book.category }}
                        </a-tag>
                        <span class="isbn">{{ book.isbn }}</span>
                      </div>

                      <div class="book-status-info">
                        <a-tag
                          :color="getStatusColor(book.status)"
                          class="status-tag"
                        >
                          {{ getStatusText(book.status) }}
                        </a-tag>
                        <span v-if="book.availableCount !== undefined" class="available-count">
                          库存: {{ book.availableCount }}
                        </span>
                      </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="book-actions">
                      <a-button
                        v-if="book.status === 'AVAILABLE'"
                        type="primary"
                        size="small"
                        :disabled="!borrowLimits.canBorrow"
                        :loading="book.borrowing"
                        @click="handleBorrowBook(book)"
                        class="borrow-btn"
                      >
                        <template #icon><BookOutlined /></template>
                        借阅
                      </a-button>
                      <a-button
                        v-else
                        disabled
                        size="small"
                        class="borrowed-btn"
                      >
                        已借出
                      </a-button>

                      <a-button
                        type="text"
                        size="small"
                        @click="viewBookDetail(book)"
                        class="detail-btn"
                      >
                        <template #icon><EyeOutlined /></template>
                        详情
                      </a-button>


                    </div>
                  </a-card>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 列表视图 -->
          <div v-else class="list-view">
            <a-list
              :data-source="books"
              :pagination="false"
              item-layout="horizontal"
            >
              <template #renderItem="{ item: book }">
                <a-list-item class="book-list-item">
                  <template #actions>
                    <a-button
                      v-if="book.status === 'AVAILABLE'"
                      type="primary"
                      size="small"
                      :disabled="!borrowLimits.canBorrow"
                      :loading="book.borrowing"
                      @click="handleBorrowBook(book)"
                    >
                      借阅
                    </a-button>
                    <a-button
                      v-else
                      disabled
                      size="small"
                    >
                      已借出
                    </a-button>
                    <a-button
                      type="link"
                      size="small"
                      @click="viewBookDetail(book)"
                    >
                      详情
                    </a-button>

                  </template>

                  <a-list-item-meta>
                    <template #avatar>
                      <div class="list-book-cover">
                        <img
                          :src="getBookCover(book)"
                          :alt="book.title"
                          @error="handleImageError"
                          loading="lazy"
                        />
                      </div>
                    </template>
                    <template #title>
                      <a @click="viewBookDetail(book)" class="book-title-link">
                        {{ book.title }}
                      </a>
                    </template>
                    <template #description>
                      <div class="book-description">
                        <p><strong>作者:</strong> {{ book.author }}</p>
                        <p><strong>分类:</strong> {{ book.category }}</p>
                        <p><strong>ISBN:</strong> {{ book.isbn }}</p>
                        <p v-if="book.description" class="book-summary">
                          {{ book.description }}
                        </p>
                        <div class="book-tags">
                          <a-tag :color="getStatusColor(book.status)">
                            {{ getStatusText(book.status) }}
                          </a-tag>
                          <a-tag :color="getCategoryColor(book.category)">
                            {{ book.category }}
                          </a-tag>
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>

          <!-- 空状态 -->
          <a-empty
            v-if="!loading && books.length === 0"
            description="暂无图书数据"
            class="empty-state"
          >
            <template #image>
              <BookOutlined style="font-size: 64px; color: #d9d9d9;" />
            </template>
            <a-button type="primary" @click="resetSearch">
              重置搜索
            </a-button>
          </a-empty>
        </a-spin>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="books.length > 0">
          <a-pagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
            :page-size-options="['12', '24', '48', '96']"
            @change="handlePageChange"
            @show-size-change="handlePageSizeChange"
            class="custom-pagination"
          />
        </div>
      </a-card>
    </div>

    <!-- 图书详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="null"
      :footer="null"
      width="800px"
      class="book-detail-modal"
      :destroy-on-close="true"
    >
      <div v-if="selectedBook" class="book-detail">
        <div class="detail-header">
          <div class="detail-cover">
            <img
              :src="getBookCover(selectedBook)"
              :alt="selectedBook.title"
              class="detail-cover-image"
              @error="handleImageError"
            />
          </div>
          <div class="detail-info">
            <h2 class="detail-title">{{ selectedBook.title }}</h2>
            <p class="detail-author">作者: {{ selectedBook.author }}</p>
            <div class="detail-tags">
              <a-tag :color="getStatusColor(selectedBook.status)" size="large">
                {{ getStatusText(selectedBook.status) }}
              </a-tag>
              <a-tag :color="getCategoryColor(selectedBook.category)" size="large">
                {{ selectedBook.category }}
              </a-tag>
            </div>
            <div class="detail-actions">
              <a-button
                v-if="selectedBook.status === 'AVAILABLE'"
                type="primary"
                size="large"
                :disabled="!borrowLimits.canBorrow"
                :loading="selectedBook.borrowing"
                @click="handleBorrowBook(selectedBook)"
                class="detail-borrow-btn"
              >
                <template #icon><BookOutlined /></template>
                立即借阅
              </a-button>
              <a-button
                v-else
                disabled
                size="large"
                class="detail-borrowed-btn"
              >
                已借出
              </a-button>


            </div>
          </div>
        </div>

        <a-divider />

        <div class="detail-content">
          <a-tabs default-active-key="info" class="detail-tabs">
            <a-tab-pane key="info" tab="基本信息">
              <a-descriptions :column="2" bordered size="middle">
                <a-descriptions-item label="ISBN" :span="2">
                  {{ selectedBook.isbn }}
                </a-descriptions-item>
                <a-descriptions-item label="出版社">
                  {{ selectedBook.publisher || '暂无信息' }}
                </a-descriptions-item>
                <a-descriptions-item label="出版日期">
                  {{ formatDate(selectedBook.publishDate) }}
                </a-descriptions-item>
                <a-descriptions-item label="页数">
                  {{ selectedBook.pageCount || '暂无信息' }}
                </a-descriptions-item>
                <a-descriptions-item label="语言">
                  {{ selectedBook.language || '中文' }}
                </a-descriptions-item>
                <a-descriptions-item label="库存数量">
                  {{ selectedBook.totalCount || '暂无信息' }}
                </a-descriptions-item>
                <a-descriptions-item label="可借数量">
                  {{ selectedBook.availableCount || '暂无信息' }}
                </a-descriptions-item>
                <a-descriptions-item label="位置">
                  {{ selectedBook.location || '暂无信息' }}
                </a-descriptions-item>
                <a-descriptions-item label="添加时间">
                  {{ formatDate(selectedBook.createdAt) }}
                </a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>

            <a-tab-pane key="description" tab="内容简介">
              <div class="book-description-content">
                <p v-if="selectedBook.description">
                  {{ selectedBook.description }}
                </p>
                <a-empty v-else description="暂无内容简介" />
              </div>
            </a-tab-pane>

            <a-tab-pane key="reviews" tab="读者评价">
              <div class="book-reviews">
                <a-empty description="暂无读者评价" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  AppstoreOutlined,
  UnorderedListOutlined,
  EyeOutlined,
  BookOutlined
} from '@ant-design/icons-vue';
import type { Book } from '@/types';
import { getUserBookList, getUserBookCategories, getUserBookDetail } from '@/api/book';
import { borrowBook, checkBookAvailability, getUserBorrowLimits } from '@/api/userBorrow';

// 响应式数据
const loading = ref(false);
const searchLoading = ref(false);
const books = ref<Book[]>([]);
const detailModalVisible = ref(false);
const selectedBook = ref<Book | null>(null);
const categories = ref<string[]>([]);
const viewMode = ref<'grid' | 'list'>('grid');
// 移除收藏功能以简化页面

const borrowLimits = ref({
  maxBorrowCount: 5,
  currentBorrowCount: 0,
  maxBorrowDays: 30,
  canBorrow: true
});

const searchForm = reactive({
  keyword: '',
  category: '',
  status: ''
});

const pagination = reactive({
  current: 1,
  pageSize: 24,
  total: 0
});

// 计算属性
const availableCount = computed(() => {
  return books.value.filter(book =>
    book.status === 'AVAILABLE' || book.status === 1
  ).length;
});

// 监听器
watch(() => searchForm.keyword, (newKeyword) => {
  if (newKeyword === '') {
    handleSearch();
  }
}, { debounce: 300 });

// 生命周期
onMounted(() => {
  loadBooks();
  loadCategories();
  loadBorrowLimits();
});

// 方法
const loadBooks = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      category: searchForm.category || undefined,
      status: searchForm.status || undefined
    };

    console.log('📚 加载图书列表 - 请求参数:', params);
    const response = await getUserBookList(params);
    console.log('📚 加载图书列表 - API完整响应:', JSON.stringify(response, null, 2));
    console.log('📚 响应数据结构检查:');
    console.log('  - response.code:', response.code);
    console.log('  - response.data:', response.data);
    console.log('  - response.data.records:', response.data?.records);
    console.log('  - records数量:', response.data?.records?.length);
    console.log('  - response.data.total:', response.data?.total);

    if (response.code === 200 && response.data && response.data.records) {
      // 修复数据映射问题
      console.log('📚 开始处理图书数据，原始records:', response.data.records);

      books.value = response.data.records.map((book: any, index: number) => {
        console.log(`📖 处理第${index + 1}本图书:`, book);
        console.log(`📖 原始状态值: ${book.status} (类型: ${typeof book.status})`);

        // 修复状态字段映射：数值转字符串
        let mappedStatus = 'UNKNOWN';
        if (book.status === 1) {
          mappedStatus = 'AVAILABLE';
          console.log(`📖 状态映射: ${book.status} -> ${mappedStatus} (可借阅)`);
        } else if (book.status === 0) {
          mappedStatus = 'BORROWED';
          console.log(`📖 状态映射: ${book.status} -> ${mappedStatus} (已借出)`);
        } else if (typeof book.status === 'string') {
          mappedStatus = book.status;
          console.log(`📖 状态保持: ${book.status} (字符串类型)`);
        } else {
          console.warn(`📖 未知状态值: ${book.status}`);
        }

        // 修复分类字段映射：categoryName -> category
        const mappedCategory = book.categoryName || book.category || '未分类';
        console.log(`📖 分类映射: categoryName="${book.categoryName}" || category="${book.category}" -> "${mappedCategory}"`);

        const mappedBook = {
          ...book,
          // 状态字段统一处理
          status: mappedStatus,
          // 分类字段统一处理
          category: mappedCategory,
          // 库存字段映射
          availableCount: book.availableQuantity || book.availableCount || 0,
          totalCount: book.totalQuantity || book.totalCount || 0,
          // 确保必要字段存在
          title: book.title || '未知书名',
          author: book.author || '未知作者',
          isbn: book.isbn || '',
          publisher: book.publisher || '',
          description: book.description || '',
          // 添加额外状态属性
          loading: false,
          borrowing: false
        };

        console.log('📖 映射后的图书数据:', mappedBook);
        return mappedBook;
      });

      pagination.total = response.data.total || 0;
      console.log('📚 图书列表加载成功 - 总数:', pagination.total, '当前页数据:', books.value.length);
    } else {
      console.error('📚 图书列表加载失败 - 响应格式错误:', response);
      message.error(response.message || '加载图书列表失败');
      books.value = [];
      pagination.total = 0;
    }
  } catch (error: any) {
    console.error('📚 图书列表加载异常:', error);
    message.error(error.message || '加载图书列表失败');
    books.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 加载图书分类
const loadCategories = async () => {
  try {
    console.log('📂 加载图书分类 - 开始请求');
    const response = await getUserBookCategories();
    console.log('📂 加载图书分类 - API响应:', response);

    if (response.code === 200 && response.data) {
      // 修复分类数据处理：处理对象数组格式
      if (Array.isArray(response.data)) {
        // 如果是对象数组，提取name字段
        categories.value = response.data.map((item: any) => {
          if (typeof item === 'string') {
            return item;
          } else if (item && typeof item === 'object') {
            return item.name || item.categoryName || item.value || String(item);
          }
          return String(item);
        }).filter(Boolean); // 过滤掉空值
      } else if (typeof response.data === 'string') {
        // 如果是字符串，按逗号分割
        categories.value = response.data.split(',').filter(Boolean);
      } else {
        // 其他情况设为空数组
        categories.value = [];
      }

      console.log('📂 分类数据处理完成:', categories.value);
    } else {
      console.warn('📂 分类数据格式错误或为空:', response);
      categories.value = [];
    }
  } catch (error) {
    console.error('📂 加载分类失败:', error);
    categories.value = [];
  }
};

// 加载借阅限制信息
const loadBorrowLimits = async () => {
  try {
    console.log('📊 加载借阅限制 - 开始请求');
    const response = await getUserBorrowLimits();
    console.log('📊 加载借阅限制 - API响应:', response);

    if (response.code === 200) {
      // 修复借阅限制数据处理
      if (typeof response.data === 'number') {
        // 如果返回的是当前借阅数量
        const currentCount = response.data;
        borrowLimits.value = {
          maxBorrowCount: 5,
          currentBorrowCount: currentCount,
          maxBorrowDays: 30,
          canBorrow: currentCount < 5
        };
      } else if (response.data && typeof response.data === 'object') {
        // 如果返回的是完整的借阅限制对象
        borrowLimits.value = {
          maxBorrowCount: response.data.maxBorrowCount || 5,
          currentBorrowCount: response.data.currentBorrowCount || 0,
          maxBorrowDays: response.data.maxBorrowDays || 30,
          canBorrow: (response.data.currentBorrowCount || 0) < (response.data.maxBorrowCount || 5)
        };
      } else {
        // 默认值
        borrowLimits.value = {
          maxBorrowCount: 5,
          currentBorrowCount: 0,
          maxBorrowDays: 30,
          canBorrow: true
        };
      }

      console.log('📊 借阅限制处理完成:', borrowLimits.value);
    } else {
      console.warn('📊 借阅限制数据格式错误:', response);
      // 设置默认值
      borrowLimits.value = {
        maxBorrowCount: 5,
        currentBorrowCount: 0,
        maxBorrowDays: 30,
        canBorrow: true
      };
    }
  } catch (error) {
    console.error('📊 加载借阅限制失败:', error);
    // 设置默认值
    borrowLimits.value = {
      maxBorrowCount: 5,
      currentBorrowCount: 0,
      maxBorrowDays: 30,
      canBorrow: true
    };
  }
};

const handleSearch = async () => {
  searchLoading.value = true;
  pagination.current = 1;
  try {
    await loadBooks();
  } finally {
    searchLoading.value = false;
  }
};

const resetSearch = async () => {
  searchForm.keyword = '';
  searchForm.category = '';
  searchForm.status = '';
  pagination.current = 1;
  await loadBooks();
  message.success('搜索条件已重置');
};

// 强制刷新数据（清除缓存）
const forceRefresh = async () => {
  console.log('🔄 强制刷新数据...');
  // 清除可能的本地缓存
  if ('caches' in window) {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
  }

  // 重新加载所有数据
  await Promise.all([
    loadBooks(),
    loadCategories(),
    loadBorrowLimits()
  ]);

  message.success('数据已刷新');
};

const handlePageChange = (page: number) => {
  pagination.current = page;
  loadBooks();
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

const handlePageSizeChange = (current: number, size: number) => {
  pagination.current = 1; // 重置到第一页
  pagination.pageSize = size;
  loadBooks();
  // 保存用户偏好
  localStorage.setItem('bookListPageSize', size.toString());
};

const handleBorrowBook = async (book: Book) => {
  if (!book || !book.id) {
    message.error('图书信息错误');
    return;
  }

  try {
    // 设置借阅状态
    book.borrowing = true;

    // 检查借阅限制
    if (!borrowLimits.value.canBorrow) {
      message.warning(`您已达到最大借阅数量限制 (${borrowLimits.value.currentBorrowCount}/${borrowLimits.value.maxBorrowCount})`);
      return;
    }

    // 检查图书可用性
    console.log('📖 检查图书可用性:', book.id);
    const availabilityResponse = await checkBookAvailability(book.id);
    console.log('📖 图书可用性检查结果:', availabilityResponse);

    if (availabilityResponse.code !== 200 || !availabilityResponse.data?.available) {
      message.warning(availabilityResponse.data?.reason || '该图书暂不可借阅');
      return;
    }

    // 执行借阅
    console.log('📖 开始借阅图书:', book.id);
    const response = await borrowBook(book.id);
    console.log('📖 借阅API响应:', response);

    if (response.code === 200) {
      message.success(`成功借阅《${book.title}》`);

      // 重新加载数据以获取最新状态
      await Promise.all([
        loadBooks(),
        loadBorrowLimits()
      ]);
    } else {
      console.error('📖 借阅失败 - 响应码:', response.code, '消息:', response.message);
      message.error(response.message || '借阅失败');
    }
  } catch (error: any) {
    console.error('📖 借阅异常:', error);
    message.error(error.message || '借阅失败，请重试');
  } finally {
    if (book) {
      book.borrowing = false;
    }
  }
};

const viewBookDetail = async (book: Book) => {
  if (!book || !book.id) {
    message.error('图书信息错误');
    return;
  }

  try {
    console.log('📖 获取图书详情:', book.id);
    // 获取详细信息
    const response = await getUserBookDetail(book.id);
    console.log('📖 图书详情API响应:', response);

    if (response.code === 200 && response.data) {
      // 修复详情数据映射
      const detailData = response.data;
      selectedBook.value = {
        ...detailData,
        // 状态字段统一处理
        status: detailData.status === 1 ? 'AVAILABLE' : detailData.status === 0 ? 'BORROWED' : detailData.status,
        // 分类字段统一处理
        category: detailData.categoryName || detailData.category || '未分类',
        // 库存字段映射
        availableCount: detailData.availableQuantity || detailData.availableCount || 0,
        totalCount: detailData.totalQuantity || detailData.totalCount || 0,
        // 确保必要字段存在
        title: detailData.title || '未知书名',
        author: detailData.author || '未知作者',
        isbn: detailData.isbn || '',
        publisher: detailData.publisher || '',
        description: detailData.description || '',
        // 添加状态属性
        borrowing: false
      };
    } else {
      console.warn('📖 图书详情获取失败，使用列表数据:', response);
      selectedBook.value = {
        ...book,
        borrowing: false
      };
    }
    detailModalVisible.value = true;
  } catch (error) {
    console.error('📖 获取图书详情异常:', error);
    selectedBook.value = {
      ...book,
      isFavorited: favoriteBooks.value.has(book.id),
      borrowing: false
    };
    detailModalVisible.value = true;
  }
};

// 视图模式切换
const setViewMode = (mode: 'grid' | 'list') => {
  viewMode.value = mode;
  localStorage.setItem('bookListViewMode', mode);
};

const toggleViewMode = () => {
  setViewMode(viewMode.value === 'grid' ? 'list' : 'grid');
};

const loadViewMode = () => {
  const savedMode = localStorage.getItem('bookListViewMode') as 'grid' | 'list';
  if (savedMode) {
    viewMode.value = savedMode;
  }

  // 加载页面大小偏好
  const savedPageSize = localStorage.getItem('bookListPageSize');
  if (savedPageSize) {
    pagination.pageSize = parseInt(savedPageSize);
  }
};

// 移除收藏功能以简化页面

// 图片处理
const getBookCover = (book: Book) => {
  // 如果有封面图片URL，使用真实图片
  if (book.coverUrl) {
    return book.coverUrl;
  }

  // 否则生成带有书名的占位图
  const colors = ['667eea', '764ba2', '5f72bd', '9b59b6', '3498db', '2ecc71'];
  const colorIndex = book.id % colors.length;
  const bgColor = colors[colorIndex];

  return `https://via.placeholder.com/300x400/${bgColor}/ffffff?text=${encodeURIComponent(book.title.substring(0, 10))}`;
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  const book = books.value.find(b => img.src.includes(b.title));
  if (book) {
    img.src = `https://via.placeholder.com/300x400/cccccc/666666?text=${encodeURIComponent('暂无封面')}`;
  }
};

// 状态处理
const getStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    'AVAILABLE': '可借阅',
    'BORROWED': '已借出',
    'RESERVED': '已预约',
    1: '可借阅',
    0: '已借出',
    2: '已预约'
  };
  return statusMap[status] || '未知状态';
};

const getStatusColor = (status: string | number) => {
  const colorMap: Record<string | number, string> = {
    'AVAILABLE': 'green',
    'BORROWED': 'red',
    'RESERVED': 'orange',
    1: 'green',
    0: 'red',
    2: 'orange'
  };
  return colorMap[status] || 'default';
};

const getCategoryColor = (category: string | undefined) => {
  if (!category || typeof category !== 'string') {
    return 'default';
  }
  const colors = ['blue', 'purple', 'cyan', 'green', 'magenta', 'orange', 'red'];
  const index = category.length % colors.length;
  return colors[index];
};

// 日期格式化
const formatDate = (dateString: string | string[] | number[]) => {
  if (!dateString) return '暂无信息';

  try {
    let date: Date;

    if (Array.isArray(dateString)) {
      // 处理 [2008, 6, 1] 格式的数组
      const [year, month, day] = dateString;
      date = new Date(year, month - 1, day); // 月份需要减1
    } else if (typeof dateString === 'string') {
      date = new Date(dateString);
    } else {
      return '暂无信息';
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '暂无信息';
    }

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.warn('日期格式化失败:', dateString, error);
    return '暂无信息';
  }
};
</script>

<style scoped>
/* 主容器 */
.user-book-list {
  padding: 0;
  min-height: 100%; /* 使用100%而不是calc，因为父容器已经处理了高度 */
  background: transparent; /* 移除背景，因为父容器已有背景 */
  position: relative;
  z-index: 1; /* 确保内容在正确的层级 */
  /* 确保内容完全可见 */
  width: 100%;
  height: 100%;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  position: relative;
  z-index: 10; /* 确保页面头部在正确的层级 */
  /* 确保页面头部完全可见，不被固定顶部栏遮挡 */
  margin-top: 0;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 24px;
  position: relative;
  z-index: 10; /* 确保搜索区域在正确的层级 */
}

.search-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
}

/* 搜索输入包装器 */
.search-input-wrapper {
  position: relative;
  z-index: 20;
}

.search-input :deep(.ant-input-search) {
  border-radius: 12px;
  position: relative;
  z-index: 20;
}

.search-input :deep(.ant-input) {
  border-radius: 12px 0 0 12px;
  border-color: #e1e8ed;
  font-size: 16px;
  position: relative;
  z-index: 20;
}

.search-input :deep(.ant-btn) {
  border-radius: 0 12px 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
  position: relative;
  z-index: 20;
}

/* 筛选器包装器 */
.filter-wrapper {
  position: relative;
  z-index: 20;
}

.filter-select {
  position: relative;
  z-index: 20;
}

.filter-select :deep(.ant-select-selector) {
  border-radius: 12px;
  border-color: #e1e8ed;
  font-size: 16px;
  position: relative;
  z-index: 20;
}

.filter-select :deep(.ant-select-arrow) {
  z-index: 25;
}

.search-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  flex-wrap: wrap;
  position: relative;
  z-index: 15; /* 确保按钮在最高层级 */
}

.reset-btn, .refresh-btn {
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
  min-width: 80px;
  position: relative;
  z-index: 15;
}

.view-toggle-btn {
  border-radius: 12px;
  border-color: #e1e8ed;
  min-width: 48px;
  position: relative;
  z-index: 15;
  background: #fff;
  transition: all 0.3s ease;
}

.view-toggle-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: scale(1.05);
}

/* 图书列表区域 */
.book-list-section {
  margin-bottom: 24px;
  position: relative;
  z-index: 5; /* 确保列表区域在正确的层级 */
}

.book-list-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 5;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-wrap: wrap;
  gap: 16px;
  position: relative;
  z-index: 10; /* 确保列表头部在正确的层级 */
}

.list-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.result-count {
  font-size: 16px;
  color: #2c3e50;
}

.search-keyword {
  font-size: 14px;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 20px;
}

.list-actions {
  position: relative;
  z-index: 15; /* 确保操作按钮在最高层级 */
}

.list-actions :deep(.ant-btn-group) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.list-actions :deep(.ant-btn) {
  position: relative;
  z-index: 15;
  transition: all 0.3s ease;
}

.list-actions :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
}

.list-actions :deep(.ant-btn:hover) {
  transform: translateY(-1px);
}

/* 网格视图 */
.grid-view {
  min-height: 400px;
}

.book-card-wrapper {
  height: 100%;
  display: flex;
}

.book-card {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid rgba(102, 126, 234, 0.1);
  flex: 1;
  min-height: 480px; /* 确保卡片最小高度一致 */
}

.book-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.book-cover {
  position: relative;
  height: 240px;
  overflow: hidden;
  cursor: pointer;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-cover:hover .cover-image {
  transform: scale(1.05);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-cover:hover .cover-overlay {
  opacity: 1;
}

.view-icon {
  font-size: 32px;
  color: white;
}

.book-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px; /* 确保内容区域最小高度 */
}

.book-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 44px; /* 固定标题高度 */
}

.book-title:hover {
  color: #667eea;
}

.book-author {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 20px; /* 固定作者高度 */
}

.book-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 32px; /* 固定元数据高度 */
}

.category-tag {
  font-size: 12px;
  border-radius: 12px;
}

.isbn {
  font-size: 12px;
  color: #95a5a6;
  font-family: 'Courier New', monospace;
}

.book-status-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  min-height: 24px; /* 固定状态信息高度 */
}

.status-tag {
  font-size: 12px;
  border-radius: 12px;
  font-weight: 600;
}

.available-count {
  font-size: 12px;
  color: #7f8c8d;
}

.book-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.borrow-btn {
  flex: 1;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
}

.borrowed-btn {
  flex: 1;
  border-radius: 8px;
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #999;
}

.detail-btn, .favorite-btn {
  border-radius: 8px;
  border-color: #e1e8ed;
}

.favorite-btn.favorited {
  color: #ff4757;
  border-color: #ff4757;
}

/* 列表视图 */
.list-view {
  min-height: 400px;
}

.book-list-item {
  border-radius: 12px;
  margin-bottom: 16px;
  padding: 20px;
  background: #fff;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.book-list-item:hover {
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
  border-color: #667eea;
}

.list-book-cover {
  width: 80px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.list-book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-title-link {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.book-title-link:hover {
  color: #667eea;
}

.book-description p {
  margin: 4px 0;
  color: #7f8c8d;
  font-size: 14px;
}

.book-summary {
  margin-top: 8px !important;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.book-tags {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  background: #fff;
  border-radius: 12px;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 32px;
  text-align: center;
  padding: 24px 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
}

.custom-pagination :deep(.ant-pagination-item) {
  border-radius: 8px;
  border-color: #e1e8ed;
}

.custom-pagination :deep(.ant-pagination-item-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.custom-pagination :deep(.ant-pagination-item-active a) {
  color: white;
}

/* 图书详情模态框 */
.book-detail-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
}

.book-detail {
  padding: 0;
}

.detail-header {
  display: flex;
  gap: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.detail-cover {
  flex-shrink: 0;
}

.detail-cover-image {
  width: 160px;
  height: 240px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.detail-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.detail-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: white;
  line-height: 1.3;
}

.detail-author {
  font-size: 18px;
  margin: 0 0 16px 0;
  color: rgba(255, 255, 255, 0.9);
}

.detail-tags {
  margin-bottom: 24px;
}

.detail-tags :deep(.ant-tag) {
  border-radius: 20px;
  font-weight: 600;
  padding: 4px 16px;
  font-size: 14px;
}

.detail-actions {
  display: flex;
  gap: 16px;
  margin-top: auto;
}

.detail-borrow-btn {
  border-radius: 12px;
  background: white;
  color: #667eea;
  border-color: white;
  font-weight: 600;
  min-width: 120px;
}

.detail-borrowed-btn {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  min-width: 120px;
}

.detail-favorite-btn {
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  min-width: 100px;
}

.detail-favorite-btn.favorited {
  background: rgba(255, 71, 87, 0.2);
  border-color: #ff4757;
  color: #ff4757;
}

.detail-content {
  padding: 24px;
}

.detail-tabs :deep(.ant-tabs-tab) {
  font-size: 16px;
  font-weight: 600;
}

.book-description-content {
  padding: 16px 0;
  font-size: 16px;
  line-height: 1.8;
  color: #2c3e50;
}

.book-reviews {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-stats {
    gap: 24px;
  }

  .stat-number {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .user-book-list {
    position: relative;
    z-index: 1;
    min-height: 100%; /* 使用100%，父容器已处理高度 */
    padding-top: 0;
    margin-top: 0;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 24px;
    position: relative;
    z-index: 10;
    margin-top: 0; /* 确保移动端页面头部不被遮挡 */
  }

  .header-content h1 {
    font-size: 28px;
  }

  .header-stats {
    gap: 16px;
    align-self: stretch;
    justify-content: space-around;
  }

  .search-section {
    position: relative;
    z-index: 10;
  }

  .search-actions {
    justify-content: stretch;
    position: relative;
    z-index: 15;
  }

  .reset-btn, .refresh-btn, .view-toggle-btn {
    flex: 1;
    position: relative;
    z-index: 15;
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    z-index: 10;
  }

  .list-actions {
    position: relative;
    z-index: 15;
  }

  /* 确保所有交互元素在移动端可见 */
  .search-input,
  .filter-select,
  .search-actions > *,
  .list-actions > * {
    position: relative;
    z-index: 15;
  }

  .book-cover {
    height: 180px;
  }

  .book-card {
    min-height: 420px;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
  }

  .detail-cover {
    align-self: center;
  }

  .detail-actions {
    flex-direction: column;
  }

  .book-list-item {
    padding: 16px;
  }

  .list-book-cover {
    width: 60px;
    height: 90px;
  }
}

@media (max-width: 576px) {
  .user-book-list {
    padding: 0 8px;
    position: relative;
    z-index: 1;
    min-height: 100%; /* 使用100%，父容器已处理高度 */
    padding-top: 0;
    margin-top: 0;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 16px;
    position: relative;
    z-index: 10;
    margin-top: 0; /* 确保小屏幕设备页面头部不被遮挡 */
  }

  .header-content h1 {
    font-size: 24px;
  }

  .search-section {
    margin-bottom: 16px;
    position: relative;
    z-index: 10;
  }

  .search-card {
    position: relative;
    z-index: 10;
  }

  .book-list-section {
    position: relative;
    z-index: 5;
  }

  /* 确保所有按钮和交互元素可见 */
  .search-actions,
  .list-actions,
  .reset-btn,
  .refresh-btn,
  .view-toggle-btn {
    position: relative;
    z-index: 15;
  }

  .book-content {
    padding: 16px;
  }

  .book-title {
    font-size: 15px;
  }

  .book-actions {
    flex-direction: column;
    gap: 8px;
  }

  .book-card {
    min-height: 400px;
  }

  .detail-cover-image {
    width: 120px;
    height: 180px;
  }

  .detail-title {
    font-size: 24px;
  }

  .detail-author {
    font-size: 16px;
  }
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.book-card.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 滚动条样式 */
:deep(.ant-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.ant-modal-body)::-webkit-scrollbar {
  width: 6px;
}

:deep(.ant-modal-body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.ant-modal-body)::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
}

:deep(.ant-modal-body)::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Ant Design 组件层级修复 */
:deep(.ant-select-dropdown) {
  z-index: 1050 !important;
}

:deep(.ant-modal-mask) {
  z-index: 1000 !important;
}

:deep(.ant-modal-wrap) {
  z-index: 1000 !important;
}

:deep(.ant-input-search) {
  position: relative;
  z-index: 15;
}

:deep(.ant-select) {
  position: relative;
  z-index: 15;
}

:deep(.ant-btn) {
  position: relative;
  z-index: 15;
}

/* 确保下拉菜单不被遮挡 */
:deep(.ant-select-open .ant-select-selector) {
  z-index: 20 !important;
}

/* 分页组件层级 */
:deep(.ant-pagination) {
  position: relative;
  z-index: 10;
}

/* 确保所有交互元素都有正确的层级 */
.search-input-wrapper,
.filter-wrapper,
.search-actions,
.list-actions,
.page-header,
.search-section,
.book-list-section {
  position: relative;
}

/* 修复可能的层级冲突 */
:deep(.ant-card) {
  position: relative;
  z-index: auto;
}

:deep(.ant-card-body) {
  position: relative;
  z-index: auto;
}

/* 确保按钮组正常显示 */
:deep(.ant-btn-group .ant-btn) {
  position: relative;
  z-index: 15;
}

/* 修复下拉菜单层级 */
:deep(.ant-select-dropdown) {
  z-index: 1060 !important;
}

/* 确保模态框在最顶层 */
:deep(.ant-modal) {
  z-index: 1000 !important;
}

:deep(.ant-modal-mask) {
  z-index: 1000 !important;
}

/* 工具提示层级 */
:deep(.ant-tooltip) {
  z-index: 1070 !important;
}

/* 消息提示层级 */
:deep(.ant-message) {
  z-index: 1080 !important;
}

/* 最终布局修复 - 确保内容完全可见 */
.user-book-list > * {
  position: relative;
  z-index: auto;
}

/* 确保页面内容不会被任何固定元素遮挡 */
.page-header,
.search-section,
.book-list-section {
  clear: both;
  display: block;
  width: 100%;
}

/* 调试用样式 - 可以临时启用来检查布局 */
/*
.page-header {
  border: 2px solid red !important;
}
.search-section {
  border: 2px solid blue !important;
}
.book-list-section {
  border: 2px solid green !important;
}
*/
</style>
