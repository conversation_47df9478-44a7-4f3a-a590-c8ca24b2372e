<template>
  <div class="user-profile">
    <div class="page-header">
      <h2>个人信息</h2>
      <p>查看和编辑您的个人资料</p>
    </div>

    <a-row :gutter="24">
      <a-col :span="8">
        <a-card title="头像信息" :bordered="false">
          <div class="avatar-section">
            <a-avatar :size="120" class="user-avatar">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
            <div class="avatar-info">
              <h3>{{ userStore.userInfo?.username }}</h3>
              <p>{{ getRoleText(userStore.userInfo?.role) }}</p>
              <a-button type="primary" size="small" @click="showAvatarModal">
                更换头像
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :span="16">
        <a-card title="基本信息" :bordered="false">
          <a-form
            :model="profileForm"
            :rules="rules"
            layout="vertical"
            @finish="handleUpdateProfile"
          >
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名" name="username">
                  <a-input
                    v-model:value="profileForm.username"
                    disabled
                    placeholder="用户名"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="邮箱" name="email">
                  <a-input
                    v-model:value="profileForm.email"
                    placeholder="请输入邮箱"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="真实姓名" name="realName">
                  <a-input
                    v-model:value="profileForm.realName"
                    placeholder="请输入真实姓名"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="手机号" name="phone">
                  <a-input
                    v-model:value="profileForm.phone"
                    placeholder="请输入手机号"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="loading">
                保存修改
              </a-button>
              <a-button style="margin-left: 8px;" @click="resetForm">
                重置
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :span="24">
        <a-card title="修改密码" :bordered="false">
          <a-form
            :model="passwordForm"
            :rules="passwordRules"
            layout="vertical"
            @finish="handleChangePassword"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="当前密码" name="currentPassword">
                  <a-input-password
                    v-model:value="passwordForm.currentPassword"
                    placeholder="请输入当前密码"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="新密码" name="newPassword">
                  <a-input-password
                    v-model:value="passwordForm.newPassword"
                    placeholder="请输入新密码"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="确认新密码" name="confirmPassword">
                  <a-input-password
                    v-model:value="passwordForm.confirmPassword"
                    placeholder="请再次输入新密码"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item>
              <a-button type="primary" html-type="submit" :loading="passwordLoading">
                修改密码
              </a-button>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
    </a-row>

    <!-- 头像上传模态框 -->
    <a-modal
      v-model:open="avatarModalVisible"
      title="更换头像"
      @ok="handleAvatarUpload"
      @cancel="avatarModalVisible = false"
    >
      <a-upload
        v-model:file-list="fileList"
        name="avatar"
        list-type="picture-card"
        class="avatar-uploader"
        :show-upload-list="false"
        :before-upload="beforeUpload"
        @change="handleChange"
      >
        <div v-if="imageUrl">
          <img :src="imageUrl" alt="avatar" style="width: 100%" />
        </div>
        <div v-else>
          <PlusOutlined />
          <div style="margin-top: 8px">上传</div>
        </div>
      </a-upload>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { UserOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import { getUserProfile, updateUserProfile, changePassword, uploadAvatar } from '@/api/user';

const userStore = useUserStore();

const loading = ref(false);
const passwordLoading = ref(false);
const avatarModalVisible = ref(false);
const imageUrl = ref('');
const fileList = ref([]);

const profileForm = reactive({
  username: '',
  email: '',
  realName: '',
  phone: ''
});

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const rules = {
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
};

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value !== passwordForm.newPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超级管理员',
    'ADMIN': '管理员',
    'USER': '普通用户'
  };
  return roleMap[role || ''] || '未知角色';
};

const handleUpdateProfile = async () => {
  loading.value = true;
  try {
    const updateData = {
      email: profileForm.email,
      realName: profileForm.realName,
      phone: profileForm.phone
    };

    const response = await updateUserProfile(updateData);
    if (response.code === 200) {
      message.success('个人信息更新成功');
      // 更新用户store中的信息
      await userStore.initUserInfo();
    } else {
      message.error(response.message || '更新失败');
    }
  } catch (error: any) {
    console.error('更新失败:', error);
    message.error(error.message || '更新失败，请重试');
  } finally {
    loading.value = false;
  }
};

const handleChangePassword = async () => {
  passwordLoading.value = true;
  try {
    const response = await changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    });

    if (response.code === 200) {
      message.success('密码修改成功');
      // 重置表单
      Object.assign(passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } else {
      message.error(response.message || '密码修改失败');
    }
  } catch (error: any) {
    console.error('密码修改失败:', error);
    message.error(error.message || '密码修改失败，请重试');
  } finally {
    passwordLoading.value = false;
  }
};

const resetForm = () => {
  // 重置为原始数据
  loadUserProfile();
};

const showAvatarModal = () => {
  avatarModalVisible.value = true;
};

const beforeUpload = (file: File) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 格式的图片!');
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

const handleChange = (info: any) => {
  if (info.file.status === 'uploading') {
    return;
  }
  if (info.file.status === 'done') {
    // 获取上传结果
    imageUrl.value = info.file.response?.url || '';
  }
};

const handleAvatarUpload = async () => {
  try {
    if (fileList.value.length === 0) {
      message.warning('请先选择头像文件');
      return;
    }

    const file = fileList.value[0].originFileObj;
    if (!file) {
      message.warning('文件上传失败，请重试');
      return;
    }

    const response = await uploadAvatar(file);
    if (response.code === 200) {
      message.success('头像更新成功');
      imageUrl.value = response.data.url;
      avatarModalVisible.value = false;
      // 更新用户信息
      await userStore.initUserInfo();
    } else {
      message.error(response.message || '头像上传失败');
    }
  } catch (error: any) {
    console.error('头像上传失败:', error);
    message.error(error.message || '头像上传失败，请重试');
  }
};

const loadUserProfile = async () => {
  try {
    const response = await getUserProfile();
    if (response.code === 200) {
      const userInfo = response.data;
      profileForm.username = userInfo.username;
      profileForm.email = userInfo.email || '';
      profileForm.realName = userInfo.realName || '';
      profileForm.phone = userInfo.phone || '';
      // 如果有头像URL，设置头像
      if (userInfo.avatar) {
        imageUrl.value = userInfo.avatar;
      }
    } else {
      message.error(response.message || '加载用户信息失败');
    }
  } catch (error: any) {
    console.error('加载用户信息失败:', error);
    // 如果API失败，使用store中的信息作为fallback
    if (userStore.userInfo) {
      profileForm.username = userStore.userInfo.username;
      profileForm.email = userStore.userInfo.email || '';
      profileForm.realName = userStore.userInfo.realName || '';
      profileForm.phone = userStore.userInfo.phone || '';
    }
  }
};

onMounted(() => {
  loadUserProfile();
});
</script>

<style scoped>
.user-profile {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.avatar-section {
  text-align: center;
}

.user-avatar {
  margin-bottom: 16px;
}

.avatar-info h3 {
  margin: 8px 0 4px 0;
  color: #333;
}

.avatar-info p {
  margin: 0 0 16px 0;
  color: #666;
}

:deep(.ant-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-uploader {
  text-align: center;
}

:deep(.avatar-uploader .ant-upload) {
  width: 128px;
  height: 128px;
}
</style>
