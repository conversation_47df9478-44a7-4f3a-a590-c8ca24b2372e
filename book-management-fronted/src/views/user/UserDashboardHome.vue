<template>
  <div class="user-dashboard-home">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-card class="welcome-card" :bordered="false">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1 class="welcome-title">
              欢迎回来，{{ userStore.userInfo?.username || '用户' }}！
            </h1>
            <p class="welcome-subtitle">
              今天是 {{ formatDate(new Date()) }}，祝您阅读愉快！
            </p>
          </div>
          <div class="welcome-avatar">
            <a-avatar :size="80" class="user-avatar">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="总借阅数"
              :value="userStats.totalBorrows"
              :value-style="{ color: '#667eea' }"
            >
              <template #prefix>
                <BookOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="当前借阅"
              :value="userStats.currentBorrows"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <ReadOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="逾期图书"
              :value="userStats.overdueBorrows"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <ExclamationCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :bordered="false">
            <a-statistic
              title="收藏图书"
              :value="userStats.favoriteBooks"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <HeartOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <a-row :gutter="[24, 24]">
      <!-- 最新图书 -->
      <a-col :xs="24" :lg="12">
        <a-card title="最新图书" :bordered="false" class="content-card">
          <template #extra>
            <a-button type="link" @click="goToBooks">查看更多</a-button>
          </template>
          <div class="book-list">
            <div
              v-for="book in latestBooks"
              :key="book.id"
              class="book-item"
              @click="viewBookDetail(book)"
            >
              <div class="book-cover">
                <img :src="getBookCover(book)" :alt="book.title" />
              </div>
              <div class="book-info">
                <h4 class="book-title">{{ book.title }}</h4>
                <p class="book-author">{{ book.author }}</p>
                <a-tag :color="getStatusColor(book.status)" size="small">
                  {{ getStatusText(book.status) }}
                </a-tag>
              </div>
            </div>
          </div>
          <a-empty v-if="latestBooks.length === 0" description="暂无最新图书" />
        </a-card>
      </a-col>

      <!-- 我的借阅 -->
      <a-col :xs="24" :lg="12">
        <a-card title="我的借阅" :bordered="false" class="content-card">
          <template #extra>
            <a-button type="link" @click="goToBorrowed">查看更多</a-button>
          </template>
          <div class="borrow-list">
            <div
              v-for="borrow in myBorrows"
              :key="borrow.id"
              class="borrow-item"
            >
              <div class="borrow-book">
                <BookOutlined class="borrow-icon" />
                <div class="borrow-info">
                  <h4 class="borrow-title">{{ borrow.bookTitle }}</h4>
                  <p class="borrow-date">
                    借阅时间：{{ formatDate(borrow.borrowDate) }}
                  </p>
                  <p class="return-date" :class="{ 'overdue': isOverdue(borrow.returnDate) }">
                    应还时间：{{ formatDate(borrow.returnDate) }}
                  </p>
                </div>
              </div>
              <div class="borrow-status">
                <a-tag
                  :color="getBorrowStatusColor(borrow.status)"
                  size="small"
                >
                  {{ getBorrowStatusText(borrow.status) }}
                </a-tag>
              </div>
            </div>
          </div>
          <a-empty v-if="myBorrows.length === 0" description="暂无借阅记录" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 快捷操作区域 -->
    <div class="quick-actions-section">
      <a-card title="快捷操作" :bordered="false" class="content-card">
        <a-row :gutter="[16, 16]">
          <a-col :xs="12" :sm="8" :md="6">
            <div class="quick-action" @click="goToBooks">
              <div class="action-icon">
                <BookOutlined />
              </div>
              <span class="action-text">浏览图书</span>
            </div>
          </a-col>
          <a-col :xs="12" :sm="8" :md="6">
            <div class="quick-action" @click="goToSearch">
              <div class="action-icon">
                <SearchOutlined />
              </div>
              <span class="action-text">搜索图书</span>
            </div>
          </a-col>
          <a-col :xs="12" :sm="8" :md="6">
            <div class="quick-action" @click="goToBorrowed">
              <div class="action-icon">
                <HistoryOutlined />
              </div>
              <span class="action-text">我的借阅</span>
            </div>
          </a-col>
          <a-col :xs="12" :sm="8" :md="6">
            <div class="quick-action" @click="goToProfile">
              <div class="action-icon">
                <UserOutlined />
              </div>
              <span class="action-text">个人信息</span>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  UserOutlined,
  BookOutlined,
  ReadOutlined,
  ExclamationCircleOutlined,
  HeartOutlined,
  SearchOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue';
import { useUserStore } from '@/stores/user';
import type { Book } from '@/types';

const router = useRouter();
const userStore = useUserStore();

const loading = ref(false);

// 用户统计数据
const userStats = reactive({
  totalBorrows: 0,
  currentBorrows: 0,
  overdueBorrows: 0,
  favoriteBooks: 0
});

// 最新图书
const latestBooks = ref<Book[]>([]);

// 我的借阅
const myBorrows = ref<any[]>([]);

// 日期格式化
const formatDate = (date: Date | string) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 检查是否逾期
const isOverdue = (returnDate: string) => {
  if (!returnDate) return false;
  return new Date(returnDate) < new Date();
};

// 获取图书封面
const getBookCover = (book: Book) => {
  if (book.coverUrl) {
    return book.coverUrl;
  }
  const colors = ['667eea', '764ba2', '5f72bd', '9b59b6'];
  const colorIndex = book.id % colors.length;
  const bgColor = colors[colorIndex];
  return `https://via.placeholder.com/120x160/${bgColor}/ffffff?text=${encodeURIComponent(book.title.substring(0, 2))}`;
};

// 获取状态文本和颜色
const getStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    'AVAILABLE': '可借阅',
    'BORROWED': '已借出',
    1: '可借阅',
    0: '已借出'
  };
  return statusMap[status] || '未知状态';
};

const getStatusColor = (status: string | number) => {
  const colorMap: Record<string | number, string> = {
    'AVAILABLE': 'green',
    'BORROWED': 'red',
    1: 'green',
    0: 'red'
  };
  return colorMap[status] || 'default';
};

const getBorrowStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'BORROWED': '借阅中',
    'RETURNED': '已归还',
    'OVERDUE': '已逾期'
  };
  return statusMap[status] || '未知状态';
};

const getBorrowStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'BORROWED': 'blue',
    'RETURNED': 'green',
    'OVERDUE': 'red'
  };
  return colorMap[status] || 'default';
};

// 导航方法
const goToBooks = () => router.push('/user/books');
const goToSearch = () => router.push('/user/search');
const goToBorrowed = () => router.push('/user/borrowed');
const goToProfile = () => router.push('/user/profile');

const viewBookDetail = (book: Book) => {
  // TODO: 实现图书详情查看
  message.info(`查看图书详情：${book.title}`);
};

// 加载数据
const loadDashboardData = async () => {
  loading.value = true;
  try {
    // TODO: 实际项目中应该从API获取数据
    // 模拟数据
    userStats.totalBorrows = 15;
    userStats.currentBorrows = 3;
    userStats.overdueBorrows = 1;
    userStats.favoriteBooks = 8;

    latestBooks.value = [
      {
        id: 1,
        title: '红楼梦',
        author: '曹雪芹',
        status: 'AVAILABLE',
        coverUrl: ''
      },
      {
        id: 2,
        title: '西游记',
        author: '吴承恩',
        status: 'BORROWED',
        coverUrl: ''
      }
    ] as Book[];

    myBorrows.value = [
      {
        id: 1,
        bookTitle: '三国演义',
        borrowDate: '2024-01-15',
        returnDate: '2024-02-15',
        status: 'BORROWED'
      },
      {
        id: 2,
        bookTitle: '水浒传',
        borrowDate: '2024-01-10',
        returnDate: '2024-01-25',
        status: 'OVERDUE'
      }
    ];
  } catch (error) {
    console.error('加载仪表板数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped>
.user-dashboard-home {
  padding: 0;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 24px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.25);
  border: none;
}

.welcome-card :deep(.ant-card-body) {
  padding: 32px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
}

.user-avatar {
  background: rgba(255, 255, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* 统计卡片区域 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
}

.stat-card :deep(.ant-statistic-title) {
  color: #64748b;
  font-weight: 500;
}

.stat-card :deep(.ant-statistic-content) {
  font-weight: 700;
}

/* 内容卡片 */
.content-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.1);
  margin-bottom: 24px;
}

.content-card :deep(.ant-card-head) {
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.content-card :deep(.ant-card-head-title) {
  color: #2c3e50;
  font-weight: 600;
  font-size: 18px;
}

/* 图书列表 */
.book-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.book-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

.book-cover {
  width: 60px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-info {
  flex: 1;
}

.book-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.book-author {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 8px 0;
}

/* 借阅列表 */
.borrow-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.borrow-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
}

.borrow-item:hover {
  background: rgba(102, 126, 234, 0.1);
}

.borrow-book {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.borrow-icon {
  font-size: 24px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 8px;
  border-radius: 8px;
}

.borrow-info {
  flex: 1;
}

.borrow-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.borrow-date,
.return-date {
  font-size: 12px;
  color: #64748b;
  margin: 2px 0;
}

.return-date.overdue {
  color: #ff4d4f;
  font-weight: 600;
}

/* 快捷操作区域 */
.quick-actions-section {
  margin-bottom: 24px;
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px 16px;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.quick-action:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-text {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .book-item,
  .borrow-item {
    padding: 12px;
  }

  .book-cover {
    width: 48px;
    height: 64px;
  }

  .book-title,
  .borrow-title {
    font-size: 14px;
  }

  .quick-action {
    padding: 16px 12px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .action-text {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .user-dashboard-home {
    padding: 0 8px;
  }

  .welcome-card :deep(.ant-card-body) {
    padding: 20px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .content-card :deep(.ant-card-body) {
    padding: 16px;
  }
}
</style>
