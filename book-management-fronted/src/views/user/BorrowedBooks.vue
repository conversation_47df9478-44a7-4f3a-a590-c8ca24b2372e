<template>
  <div class="user-borrowed-books">
    <div class="page-header">
      <h2>我的借阅</h2>
      <p>查看您的借阅历史和当前借阅状态</p>

      <!-- 调试信息 - 开发环境显示 -->
      <div v-if="showDebugInfo" class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px;">
        <h4>调试信息</h4>
        <p><strong>当前时间:</strong> {{ new Date().toISOString() }}</p>
        <p><strong>借阅记录数量:</strong> {{ currentBorrows.length }}</p>
        <div v-if="currentBorrows.length > 0">
          <h5>第一条记录详情:</h5>
          <pre>{{ JSON.stringify(currentBorrows[0], null, 2) }}</pre>
          <p><strong>剩余天数计算:</strong> {{ calculateRemainingDays(currentBorrows[0].dueDate) }}</p>
          <p><strong>状态信息:</strong> {{ JSON.stringify(getRemainingDaysInfo(currentBorrows[0].dueDate), null, 2) }}</p>
        </div>
      </div>
    </div>

    <!-- 借阅统计 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="总借阅次数"
            :value="borrowStats.totalBorrows"
            :value-style="{ color: '#3f8600' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="当前借阅"
            :value="borrowStats.currentBorrows"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="逾期图书"
            :value="borrowStats.overdueBooks"
            :value-style="{ color: '#cf1322' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="已归还"
            :value="borrowStats.returnedBooks"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <a-tabs v-model:activeKey="activeTab" type="card">
      <a-tab-pane key="current" tab="当前借阅">
        <a-card :bordered="false">
          <a-table
            :columns="currentColumns"
            :data-source="currentBorrows"
            :pagination="false"
            :loading="loading"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'remainingDays'">
                <div class="remaining-days-cell">
                  <span :class="getRemainingDaysInfo(record.dueDate).color">
                    {{ getRemainingDaysInfo(record.dueDate).text }}
                  </span>
                  <div v-if="getRemainingDaysInfo(record.dueDate).urgency === 'critical'"
                       class="urgency-indicator critical">
                    🚨
                  </div>
                  <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'high'"
                       class="urgency-indicator high">
                    ⚠️
                  </div>
                  <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'medium'"
                       class="urgency-indicator medium">
                    ⏰
                  </div>
                </div>
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'action'">
                <a-space direction="vertical" size="small">
                  <!-- 主要操作按钮 -->
                  <a-space>
                    <a-button
                      v-if="record.status === 'BORROWED'"
                      type="primary"
                      size="small"
                      @click="handleReturnBook(record)"
                    >
                      归还
                    </a-button>
                    <a-button
                      v-else-if="record.status === 'OVERDUE'"
                      type="primary"
                      danger
                      size="small"
                      @click="handleReturnBook(record)"
                    >
                      逾期归还
                    </a-button>
                    <a-button
                      v-if="record.status === 'BORROWED' && getRemainingDaysInfo(record.dueDate).urgency !== 'critical'"
                      size="small"
                      type="default"
                      @click="handleRenewBook(record)"
                    >
                      续借
                    </a-button>
                  </a-space>

                  <!-- 到期提醒 -->
                  <div v-if="getRemainingDaysInfo(record.dueDate).urgency === 'critical'"
                       class="due-reminder critical">
                    <a-tag color="red" size="small">
                      {{ getRemainingDaysInfo(record.dueDate).status === 'overdue' ? '已逾期' : '今日到期' }}
                    </a-tag>
                  </div>
                  <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'high'"
                       class="due-reminder high">
                    <a-tag color="orange" size="small">明日到期</a-tag>
                  </div>
                  <div v-else-if="getRemainingDaysInfo(record.dueDate).urgency === 'medium'"
                       class="due-reminder medium">
                    <a-tag color="gold" size="small">即将到期</a-tag>
                  </div>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>

      <a-tab-pane key="history" tab="借阅历史">
        <a-card :bordered="false">
          <a-table
            :columns="historyColumns"
            :data-source="borrowHistory"
            :pagination="{
              current: historyPagination.current,
              pageSize: historyPagination.pageSize,
              total: historyPagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }"
            :loading="loading"
            @change="handleHistoryPageChange"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'borrowDays'">
                {{ calculateBorrowDays(record.borrowDate, record.returnDate) }} 天
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import type { BorrowRecord } from '@/types';
import { getCurrentBorrows, getBorrowHistory, returnBook, renewBook } from '@/api/userBorrow';
import { getUserBorrowStats } from '@/api/user';

const activeTab = ref('current');
const loading = ref(false);
const currentBorrows = ref<BorrowRecord[]>([]);
const borrowHistory = ref<BorrowRecord[]>([]);
const borrowStats = ref({
  totalBorrows: 0,
  currentBorrows: 0,
  overdueBooks: 0,
  returnedBooks: 0
});

const historyPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
});

// 实时更新定时器
const updateTimer = ref<NodeJS.Timeout | null>(null);
const currentTime = ref(new Date());

// 调试信息开关
const showDebugInfo = ref(true); // 开发环境显示调试信息

// 格式化日期显示 - 增强版
const formatDate = (dateString: string, includeTime: boolean = false) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';

    if (includeTime) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

// 格式化友好的日期显示（如：2024年1月15日）
const formatFriendlyDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('友好日期格式化错误:', error);
    return '-';
  }
};

// 计算相对时间（如：3天前、2小时前）
const formatRelativeTime = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    const now = new Date();
    if (isNaN(date.getTime())) return '-';

    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  } catch (error) {
    console.error('相对时间格式化错误:', error);
    return '-';
  }
};

const currentColumns = [
  {
    title: '图书名称',
    dataIndex: ['book', 'title'],
    key: 'bookTitle',
    width: 200,
  },
  {
    title: '作者',
    dataIndex: ['book', 'author'],
    key: 'bookAuthor',
    width: 120,
  },
  {
    title: '借阅时间',
    key: 'borrowDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      return `${formatFriendlyDate(record.borrowDate)} (${formatRelativeTime(record.borrowDate)})`;
    }
  },
  {
    title: '归还截止',
    key: 'dueDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      return `${formatFriendlyDate(record.dueDate)}`;
    }
  },
  {
    title: '剩余天数',
    key: 'remainingDays',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 160,
  },
];

const historyColumns = [
  {
    title: '图书名称',
    dataIndex: ['book', 'title'],
    key: 'bookTitle',
    width: 200,
  },
  {
    title: '作者',
    dataIndex: ['book', 'author'],
    key: 'bookAuthor',
    width: 120,
  },
  {
    title: '借阅时间',
    key: 'borrowDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      console.log('🕐 渲染借阅时间:', record.borrowDate);
      if (!record.borrowDate) {
        console.warn('⚠️ 借阅时间为空:', record);
        return '-';
      }
      return formatDate(record.borrowDate, true);
    }
  },
  {
    title: '归还时间',
    key: 'returnDate',
    width: 140,
    customRender: ({ record }: { record: BorrowRecord }) => {
      console.log('🕐 渲染归还时间:', record.returnDate);
      if (!record.returnDate) {
        return record.status === 'RETURNED' ? '未记录' : '未归还';
      }
      return formatDate(record.returnDate, true);
    }
  },
  {
    title: '借阅天数',
    key: 'borrowDays',
    width: 100,
    customRender: ({ record }: { record: BorrowRecord }) => {
      if (record.borrowDate && record.returnDate) {
        const days = calculateBorrowDays(record.borrowDate, record.returnDate);
        return `${days} 天`;
      }
      return '-';
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
  },
];

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'BORROWED': '借阅中',
    'RETURNED': '已归还',
    'OVERDUE': '已逾期'
  };
  return statusMap[status] || status;
};

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'BORROWED': 'blue',
    'RETURNED': 'green',
    'OVERDUE': 'red'
  };
  return colorMap[status] || 'default';
};

// 计算剩余天数 - 增强版
const calculateRemainingDays = (dueDate: string) => {
  console.log('计算剩余天数，输入日期:', dueDate);

  if (!dueDate) {
    console.warn('到期日期为空');
    return 0;
  }

  try {
    const due = new Date(dueDate);
    const now = new Date();

    // 检查日期是否有效
    if (isNaN(due.getTime())) {
      console.warn('无效的到期日期:', dueDate);
      return 0;
    }

    // 设置时间为当天的开始，避免时间部分影响计算
    const dueDay = new Date(due.getFullYear(), due.getMonth(), due.getDate());
    const nowDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const diffTime = dueDay.getTime() - nowDay.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    console.log('计算结果 - 到期日期:', dueDay, '当前日期:', nowDay, '剩余天数:', diffDays);
    return diffDays;
  } catch (error) {
    console.error('计算剩余天数时出错:', error);
    return 0;
  }
};

// 获取剩余天数的状态文本和颜色
const getRemainingDaysInfo = (dueDate: string) => {
  const remainingDays = calculateRemainingDays(dueDate);

  if (remainingDays < 0) {
    return {
      text: `逾期 ${Math.abs(remainingDays)} 天`,
      color: 'text-red',
      status: 'overdue',
      urgency: 'critical'
    };
  } else if (remainingDays === 0) {
    return {
      text: '今日到期',
      color: 'text-red',
      status: 'due-today',
      urgency: 'critical'
    };
  } else if (remainingDays === 1) {
    return {
      text: '明日到期',
      color: 'text-orange',
      status: 'due-tomorrow',
      urgency: 'high'
    };
  } else if (remainingDays <= 3) {
    return {
      text: `${remainingDays} 天后到期`,
      color: 'text-orange',
      status: 'due-soon',
      urgency: 'medium'
    };
  } else if (remainingDays <= 7) {
    return {
      text: `${remainingDays} 天`,
      color: 'text-yellow',
      status: 'normal',
      urgency: 'low'
    };
  } else {
    return {
      text: `${remainingDays} 天`,
      color: 'text-green',
      status: 'safe',
      urgency: 'none'
    };
  }
};

// 计算借阅天数
const calculateBorrowDays = (borrowDate: string, returnDate: string) => {
  if (!borrowDate || !returnDate) {
    return 0;
  }

  try {
    const borrow = new Date(borrowDate);
    const returnD = new Date(returnDate);

    if (isNaN(borrow.getTime()) || isNaN(returnD.getTime())) {
      return 0;
    }

    const diffTime = returnD.getTime() - borrow.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  } catch (error) {
    console.error('计算借阅天数时出错:', error);
    return 0;
  }
};

const handleReturnBook = async (record: BorrowRecord) => {
  try {
    console.log('开始归还图书:', record);
    const response = await returnBook(record.id);
    console.log('归还图书API响应:', response);

    if (response.code === 200) {
      message.success(`《${record.book.title}》归还成功`);
      // 立即从当前借阅列表中移除该记录
      currentBorrows.value = currentBorrows.value.filter(item => item.id !== record.id);
      // 重新加载所有数据
      await loadBorrowData();
    } else {
      console.error('归还失败 - 响应码:', response.code, '消息:', response.message);
      message.error(response.message || '归还失败');
    }
  } catch (error: any) {
    console.error('归还失败:', error);
    message.error(error.message || '归还失败，请重试');
  }
};

const handleRenewBook = async (record: BorrowRecord) => {
  try {
    console.log('开始续借图书:', record);
    const response = await renewBook(record.id);
    console.log('续借图书API响应:', response);

    if (response.code === 200) {
      message.success(`《${record.book.title}》续借成功`);
      // 重新加载数据以获取更新的到期时间
      await loadBorrowData();
    } else {
      console.error('续借失败 - 响应码:', response.code, '消息:', response.message);
      message.error(response.message || '续借失败');
    }
  } catch (error: any) {
    console.error('续借失败:', error);
    message.error(error.message || '续借失败，请重试');
  }
};

const loadCurrentBorrows = async () => {
  try {
    console.log('开始加载当前借阅记录...');
    const response = await getCurrentBorrows();
    console.log('当前借阅记录API响应:', response);

    if (response.code === 200) {
      currentBorrows.value = response.data;
      console.log('当前借阅记录数据:', response.data);
      console.log('当前借阅记录数量:', response.data.length);
    } else {
      console.error('加载当前借阅失败 - 响应码:', response.code, '消息:', response.message);
      message.error(response.message || '加载当前借阅失败');
    }
  } catch (error: any) {
    console.error('加载当前借阅失败:', error);
    message.error(error.message || '加载当前借阅失败');
  }
};

const loadBorrowHistory = async () => {
  try {
    console.log('开始加载借阅历史...');
    const response = await getBorrowHistory({
      current: historyPagination.value.current,
      size: historyPagination.value.pageSize
    });
    console.log('借阅历史API响应:', response);

    if (response.code === 200) {
      borrowHistory.value = response.data.records;
      historyPagination.value.total = response.data.total;
      console.log('借阅历史数据:', response.data.records);
      console.log('借阅历史总数:', response.data.total);
    } else {
      console.error('加载借阅历史失败 - 响应码:', response.code, '消息:', response.message);
      message.error(response.message || '加载借阅历史失败');
    }
  } catch (error: any) {
    console.error('加载借阅历史失败:', error);
    message.error(error.message || '加载借阅历史失败');
  }
};

const loadBorrowStats = async () => {
  try {
    const response = await getUserBorrowStats();
    if (response.code === 200) {
      borrowStats.value = response.data;
    }
  } catch (error) {
    console.error('加载借阅统计失败:', error);
  }
};

const loadBorrowData = async () => {
  loading.value = true;
  try {
    await Promise.all([
      loadCurrentBorrows(),
      loadBorrowHistory(),
      loadBorrowStats()
    ]);
  } finally {
    loading.value = false;
  }
};

const handleHistoryPageChange = (page: number, pageSize: number) => {
  historyPagination.value.current = page;
  historyPagination.value.pageSize = pageSize;
  loadBorrowHistory();
};

// 启动实时更新定时器
const startUpdateTimer = () => {
  updateTimer.value = setInterval(() => {
    currentTime.value = new Date();
    // 每分钟检查一次是否有图书即将到期或已逾期
    checkDueReminders();
  }, 60000); // 每分钟更新一次
};

// 停止实时更新定时器
const stopUpdateTimer = () => {
  if (updateTimer.value) {
    clearInterval(updateTimer.value);
    updateTimer.value = null;
  }
};

// 检查到期提醒
const checkDueReminders = () => {
  const criticalBooks = currentBorrows.value.filter(book => {
    const info = getRemainingDaysInfo(book.dueDate);
    return info.urgency === 'critical';
  });

  if (criticalBooks.length > 0) {
    const overdueBooks = criticalBooks.filter(book =>
      getRemainingDaysInfo(book.dueDate).status === 'overdue'
    );
    const dueTodayBooks = criticalBooks.filter(book =>
      getRemainingDaysInfo(book.dueDate).status === 'due-today'
    );

    if (overdueBooks.length > 0) {
      message.warning(`您有 ${overdueBooks.length} 本图书已逾期，请尽快归还！`);
    } else if (dueTodayBooks.length > 0) {
      message.warning(`您有 ${dueTodayBooks.length} 本图书今日到期，请及时归还！`);
    }
  }
};

onMounted(() => {
  loadBorrowData();
  startUpdateTimer();
});

onUnmounted(() => {
  stopUpdateTimer();
});
</script>

<style scoped>
.user-borrowed-books {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

:deep(.ant-tabs-card .ant-tabs-tab) {
  border-radius: 8px 8px 0 0;
}

:deep(.ant-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
}

/* 时间显示相关样式 */
.text-red {
  color: #ff4d4f;
  font-weight: 600;
}

.text-orange {
  color: #fa8c16;
  font-weight: 600;
}

.text-yellow {
  color: #faad14;
  font-weight: 600;
}

.text-green {
  color: #52c41a;
  font-weight: 600;
}

/* 剩余天数单元格样式 */
.remaining-days-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.urgency-indicator {
  font-size: 16px;
  line-height: 1;
}

.urgency-indicator.critical {
  animation: blink 1s infinite;
}

.urgency-indicator.high {
  animation: pulse 2s infinite;
}

.urgency-indicator.medium {
  opacity: 0.8;
}

/* 到期提醒样式 */
.due-reminder {
  margin-top: 4px;
}

.due-reminder.critical {
  animation: shake 0.5s infinite;
}

/* 动画效果 */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* 表格行样式增强 */
:deep(.ant-table-tbody > tr.overdue-row) {
  background-color: #fff2f0;
  border-left: 4px solid #ff4d4f;
}

:deep(.ant-table-tbody > tr.due-soon-row) {
  background-color: #fff7e6;
  border-left: 4px solid #fa8c16;
}

:deep(.ant-table-tbody > tr.due-today-row) {
  background-color: #fff1f0;
  border-left: 4px solid #ff4d4f;
  animation: highlight 2s infinite;
}

@keyframes highlight {
  0%, 100% { background-color: #fff1f0; }
  50% { background-color: #ffe7e6; }
}
</style>
