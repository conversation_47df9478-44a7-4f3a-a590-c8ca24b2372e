<template>
  <div class="user-favorites">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>我的收藏</h1>
        <p>管理您收藏的图书</p>
      </div>
      <div class="header-stats">
        <div class="stat-item">
          <span class="stat-number">{{ favorites.length }}</span>
          <span class="stat-label">收藏图书</span>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <a-card class="search-card" :bordered="false">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="16" :md="18">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索收藏的图书..."
              enter-button="搜索"
              size="large"
              @search="handleSearch"
              class="search-input"
            />
          </a-col>
          <a-col :xs="24" :sm="8" :md="6">
            <div class="search-actions">
              <a-button
                type="primary"
                size="large"
                @click="resetSearch"
                class="reset-btn"
              >
                重置
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 收藏列表 -->
    <div class="favorites-section">
      <a-card class="favorites-card" :bordered="false">
        <a-spin :spinning="loading" size="large">
          <!-- 网格视图 -->
          <div class="favorites-grid">
            <a-row :gutter="[20, 20]">
              <a-col
                v-for="favorite in filteredFavorites"
                :key="favorite.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
                :xxl="4"
              >
                <div class="favorite-card-wrapper">
                  <a-card
                    class="favorite-card"
                    :hoverable="true"
                  >
                    <!-- 图书封面 -->
                    <div class="book-cover" @click="viewBookDetail(favorite.book)">
                      <img
                        :src="getBookCover(favorite.book)"
                        :alt="favorite.book.title"
                        class="cover-image"
                        loading="lazy"
                      />
                      <div class="cover-overlay">
                        <EyeOutlined class="view-icon" />
                      </div>
                    </div>

                    <!-- 图书信息 -->
                    <div class="book-content">
                      <h3 class="book-title" @click="viewBookDetail(favorite.book)">
                        {{ favorite.book.title }}
                      </h3>
                      <p class="book-author">{{ favorite.book.author }}</p>

                      <div class="book-meta">
                        <a-tag
                          :color="getCategoryColor(favorite.book.category)"
                          class="category-tag"
                        >
                          {{ favorite.book.category }}
                        </a-tag>
                      </div>

                      <div class="favorite-info">
                        <span class="favorite-date">
                          收藏于：{{ formatDate(favorite.createdAt) }}
                        </span>
                      </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="book-actions">
                      <a-button
                        v-if="favorite.book.status === 'AVAILABLE'"
                        type="primary"
                        size="small"
                        @click="handleBorrowBook(favorite.book)"
                        class="borrow-btn"
                      >
                        <template #icon><BookOutlined /></template>
                        借阅
                      </a-button>
                      <a-button
                        v-else
                        disabled
                        size="small"
                        class="borrowed-btn"
                      >
                        已借出
                      </a-button>

                      <a-button
                        type="text"
                        size="small"
                        @click="removeFavorite(favorite)"
                        class="remove-btn"
                        danger
                      >
                        <template #icon><HeartFilled /></template>
                        取消收藏
                      </a-button>
                    </div>
                  </a-card>
                </div>
              </a-col>
            </a-row>
          </div>

          <!-- 空状态 -->
          <a-empty
            v-if="!loading && filteredFavorites.length === 0"
            description="暂无收藏的图书"
            class="empty-state"
          >
            <template #image>
              <HeartOutlined style="font-size: 64px; color: #d9d9d9;" />
            </template>
            <a-button type="primary" @click="goToBooks">
              去浏览图书
            </a-button>
          </a-empty>
        </a-spin>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  EyeOutlined,
  BookOutlined,
  HeartOutlined,
  HeartFilled
} from '@ant-design/icons-vue';
import type { Book } from '@/types';

const router = useRouter();

const loading = ref(false);
const searchKeyword = ref('');

// 收藏数据
const favorites = ref<any[]>([]);

// 过滤后的收藏
const filteredFavorites = computed(() => {
  if (!searchKeyword.value) {
    return favorites.value;
  }
  
  const keyword = searchKeyword.value.toLowerCase();
  return favorites.value.filter(favorite => 
    favorite.book.title.toLowerCase().includes(keyword) ||
    favorite.book.author.toLowerCase().includes(keyword)
  );
});

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
};

const resetSearch = () => {
  searchKeyword.value = '';
};

// 获取图书封面
const getBookCover = (book: Book) => {
  if (book.coverUrl) {
    return book.coverUrl;
  }
  const colors = ['667eea', '764ba2', '5f72bd', '9b59b6'];
  const colorIndex = book.id % colors.length;
  const bgColor = colors[colorIndex];
  return `https://via.placeholder.com/300x400/${bgColor}/ffffff?text=${encodeURIComponent(book.title.substring(0, 10))}`;
};

const getCategoryColor = (category: string | undefined) => {
  if (!category || typeof category !== 'string') {
    return 'default';
  }
  const colors = ['blue', 'purple', 'cyan', 'green', 'magenta', 'orange', 'red'];
  const index = category.length % colors.length;
  return colors[index];
};

// 日期格式化
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 查看图书详情
const viewBookDetail = (book: Book) => {
  message.info(`查看图书详情：${book.title}`);
};

// 借阅图书
const handleBorrowBook = (book: Book) => {
  message.info(`借阅图书：${book.title}`);
};

// 取消收藏
const removeFavorite = (favorite: any) => {
  const index = favorites.value.findIndex(f => f.id === favorite.id);
  if (index > -1) {
    favorites.value.splice(index, 1);
    message.success('已取消收藏');
  }
};

// 跳转到图书浏览
const goToBooks = () => {
  router.push('/user/books');
};

// 加载收藏数据
const loadFavorites = async () => {
  loading.value = true;
  try {
    // TODO: 实际项目中应该从API获取数据
    // 模拟数据
    favorites.value = [
      {
        id: 1,
        book: {
          id: 1,
          title: '红楼梦',
          author: '曹雪芹',
          category: '古典文学',
          status: 'AVAILABLE',
          coverUrl: ''
        },
        createdAt: '2024-01-15'
      },
      {
        id: 2,
        book: {
          id: 2,
          title: '西游记',
          author: '吴承恩',
          category: '古典文学',
          status: 'BORROWED',
          coverUrl: ''
        },
        createdAt: '2024-01-10'
      }
    ];
  } catch (error) {
    console.error('加载收藏失败:', error);
    message.error('加载收藏失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadFavorites();
});
</script>

<style scoped>
/* 主容器 */
.user-favorites {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
  font-weight: 400;
}

.header-stats {
  display: flex;
  gap: 32px;
  align-items: center;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 28px;
  font-weight: 700;
  color: #667eea;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 24px;
}

.search-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-input :deep(.ant-input-search) {
  border-radius: 12px;
}

.search-input :deep(.ant-input) {
  border-radius: 12px 0 0 12px;
  border-color: #e1e8ed;
  font-size: 16px;
}

.search-input :deep(.ant-btn) {
  border-radius: 0 12px 12px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
}

.search-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.reset-btn {
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
  min-width: 80px;
}

/* 收藏列表区域 */
.favorites-section {
  margin-bottom: 24px;
}

.favorites-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 收藏网格 */
.favorites-grid {
  min-height: 400px;
}

.favorite-card-wrapper {
  height: 100%;
  display: flex;
}

.favorite-card {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid rgba(102, 126, 234, 0.1);
  flex: 1;
  min-height: 480px;
}

.favorite-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
  border-color: #667eea;
}

.book-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  flex-shrink: 0;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-cover:hover .cover-image {
  transform: scale(1.05);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-cover:hover .cover-overlay {
  opacity: 1;
}

.view-icon {
  font-size: 32px;
  color: white;
}

.book-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.book-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.4;
  cursor: pointer;
  transition: color 0.3s ease;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 44px;
}

.book-title:hover {
  color: #667eea;
}

.book-author {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 20px;
}

.book-meta {
  margin-bottom: 12px;
  min-height: 32px;
}

.category-tag {
  font-size: 12px;
  border-radius: 12px;
}

.favorite-info {
  margin-bottom: 16px;
  min-height: 24px;
}

.favorite-date {
  font-size: 12px;
  color: #95a5a6;
}

.book-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.borrow-btn {
  flex: 1;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  font-weight: 600;
}

.borrowed-btn {
  flex: 1;
  border-radius: 8px;
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #999;
}

.remove-btn {
  border-radius: 8px;
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.remove-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 空状态 */
.empty-state {
  padding: 60px 20px;
  background: #fff;
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 24px;
  }

  .header-content h1 {
    font-size: 28px;
  }

  .header-stats {
    gap: 16px;
    align-self: stretch;
    justify-content: space-around;
  }

  .search-actions {
    justify-content: stretch;
  }

  .reset-btn {
    flex: 1;
  }

  .book-cover {
    height: 180px;
  }

  .favorite-card {
    min-height: 420px;
  }
}

@media (max-width: 576px) {
  .user-favorites {
    padding: 0 8px;
  }

  .page-header {
    padding: 20px;
    margin-bottom: 16px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .search-section {
    margin-bottom: 16px;
  }

  .book-content {
    padding: 16px;
  }

  .book-title {
    font-size: 15px;
  }

  .book-actions {
    flex-direction: column;
    gap: 8px;
  }

  .favorite-card {
    min-height: 400px;
  }
}
</style>
