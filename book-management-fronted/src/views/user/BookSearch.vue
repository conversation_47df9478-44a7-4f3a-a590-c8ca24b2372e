<template>
  <div class="user-book-search">
    <div class="page-header">
      <h2>图书搜索</h2>
      <p>高级搜索功能，帮您快速找到想要的图书</p>
    </div>

    <a-card class="search-card" :bordered="false">
      <a-form
        :model="searchForm"
        layout="vertical"
        @finish="handleSearch"
      >
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="关键词">
              <a-input
                v-model:value="searchForm.keyword"
                placeholder="输入书名、作者、ISBN等"
                size="large"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="分类">
              <a-select
                v-model:value="searchForm.category"
                placeholder="选择分类"
                size="large"
                style="width: 100%"
              >
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option
                  v-for="category in categories"
                  :key="category"
                  :value="category"
                >
                  {{ category }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="作者">
              <a-input
                v-model:value="searchForm.author"
                placeholder="作者姓名"
                size="large"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="出版社">
              <a-input
                v-model:value="searchForm.publisher"
                placeholder="出版社名称"
                size="large"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="操作">
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                style="width: 100%"
                :loading="loading"
              >
                搜索
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <a-card v-if="searchResults.length > 0" class="results-card" :bordered="false">
      <h3>搜索结果 ({{ pagination.total }} 本)</h3>
      <a-spin :spinning="loading">
        <a-list
          :data-source="searchResults"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }"
          item-layout="horizontal"
          @change="handlePageChange"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button
                  v-if="item.status === 'AVAILABLE'"
                  type="primary"
                  size="small"
                  :disabled="!borrowLimits.canBorrow"
                  @click="handleBorrowBook(item)"
                >
                  借阅
                </a-button>
                <a-button v-else disabled size="small">
                  已借出
                </a-button>
              </template>
              <a-list-item-meta
                :title="item.title"
                :description="`作者: ${item.author} | 分类: ${item.category} | ISBN: ${item.isbn} | 出版社: ${item.publisher}`"
              />
              <div class="book-status">
                <a-tag :color="item.status === 'AVAILABLE' ? 'green' : 'red'">
                  {{ item.status === 'AVAILABLE' ? '可借阅' : '已借出' }}
                </a-tag>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </a-spin>
    </a-card>

    <a-empty v-else-if="hasSearched" description="未找到相关图书" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { Book } from '@/types';
import { searchBooks, getUserBookCategories } from '@/api/book';
import { borrowBook, checkBookAvailability, getUserBorrowLimits } from '@/api/userBorrow';

const searchForm = reactive({
  keyword: '',
  category: '',
  author: '',
  publisher: '',
  isbn: ''
});

const searchResults = ref<Book[]>([]);
const hasSearched = ref(false);
const loading = ref(false);
const categories = ref<string[]>([]);
const borrowLimits = ref({
  maxBorrowCount: 5,
  currentBorrowCount: 0,
  canBorrow: true
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 生命周期
onMounted(() => {
  loadCategories();
  loadBorrowLimits();
});

// 方法
const loadCategories = async () => {
  try {
    const response = await getUserBookCategories();
    if (response.code === 200) {
      categories.value = response.data;
    }
  } catch (error) {
    console.error('加载分类失败:', error);
  }
};

const loadBorrowLimits = async () => {
  try {
    const response = await getUserBorrowLimits();
    if (response.code === 200) {
      borrowLimits.value = response.data;
    }
  } catch (error) {
    console.error('加载借阅限制失败:', error);
  }
};

const handleSearch = async () => {
  try {
    loading.value = true;
    hasSearched.value = true;
    pagination.current = 1;

    const params = {
      keyword: searchForm.keyword || undefined,
      category: searchForm.category || undefined,
      author: searchForm.author || undefined,
      publisher: searchForm.publisher || undefined,
      isbn: searchForm.isbn || undefined,
      current: pagination.current,
      size: pagination.pageSize
    };

    const response = await searchBooks(params);

    if (response.code === 200) {
      searchResults.value = response.data.records;
      pagination.total = response.data.total;

      if (searchResults.value.length === 0) {
        message.info('未找到符合条件的图书');
      } else {
        message.success(`找到 ${pagination.total} 本相关图书`);
      }
    } else {
      message.error(response.message || '搜索失败');
    }
  } catch (error: any) {
    console.error('搜索失败:', error);
    message.error(error.message || '搜索失败，请重试');
  } finally {
    loading.value = false;
  }
};

const handleBorrowBook = async (book: Book) => {
  try {
    if (!borrowLimits.value.canBorrow) {
      message.warning('您已达到最大借阅数量限制');
      return;
    }

    const availabilityResponse = await checkBookAvailability(book.id);
    if (availabilityResponse.code !== 200 || !availabilityResponse.data.available) {
      message.warning(availabilityResponse.data.reason || '该图书暂不可借阅');
      return;
    }

    const response = await borrowBook(book.id);
    if (response.code === 200) {
      message.success(`成功借阅《${book.title}》`);
      handleSearch(); // 重新搜索以更新状态
      loadBorrowLimits(); // 更新借阅限制信息
    } else {
      message.error(response.message || '借阅失败');
    }
  } catch (error: any) {
    console.error('借阅失败:', error);
    message.error(error.message || '借阅失败，请重试');
  }
};

const handlePageChange = (page: number, pageSize: number) => {
  pagination.current = page;
  pagination.pageSize = pageSize;
  handleSearch();
};
</script>

<style scoped>
.user-book-search {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.search-card,
.results-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-card h3 {
  margin-bottom: 16px;
  color: #333;
}
</style>
