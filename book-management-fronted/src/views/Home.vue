<template>
  <div class="home">
    <div class="container">
      <h1>图书管理系统</h1>
      <p>欢迎使用图书管理系统</p>
      <div class="actions">
        <a-button type="primary" size="large" @click="goToLogin">
          登录系统
        </a-button>
        <a-button size="large" @click="goToRegister">
          注册账号
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const router = useRouter();

const goToLogin = () => {
  router.push('/login');
};

const goToRegister = () => {
  router.push('/register');
};
</script>

<style scoped>
.home {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  text-align: center;
  background: white;
  padding: 60px 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.container h1 {
  font-size: 28px;
  color: #1f2937;
  margin-bottom: 16px;
  font-weight: 600;
}

.container p {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 32px;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.actions .ant-btn {
  height: 48px;
  font-size: 16px;
  border-radius: 8px;
}

.actions .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.actions .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

@media (max-width: 480px) {
  .container {
    margin: 20px;
    padding: 40px 24px;
  }
}
</style>
