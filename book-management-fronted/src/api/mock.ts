import type { ApiResponse, LoginForm, RegisterForm, LoginResponse, User } from '@/types';

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟用户数据（基于真实数据库数据）
const mockUsers: User[] = [
  {
    id: 1,
    username: 'kyx666',
    realName: '超级管理员',
    email: '<EMAIL>',
    phone: null,
    role: 'SUPER_ADMIN',
    status: 1, // 1-启用
    createdTime: '2025-06-08 01:35:59',
    lastLoginTime: null
  },
  {
    id: 2,
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    role: 'ADMIN',
    status: 1, // 1-启用
    createdTime: '2024-01-01 10:00:00',
    lastLoginTime: '2024-06-08 10:00:00'
  },
  {
    id: 3,
    username: 'user',
    realName: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    role: 'USER',
    status: 1, // 1-启用
    createdTime: '2024-01-02 10:00:00',
    lastLoginTime: '2024-06-07 15:30:00'
  }
];

// 模拟登录API
export const mockLogin = async (data: LoginForm): Promise<ApiResponse<LoginResponse>> => {
  await delay(1000); // 模拟网络延迟

  // 简单的用户验证
  const user = mockUsers.find(u => u.username === data.username);
  
  if (!user) {
    return {
      code: 400,
      message: '用户名不存在',
      data: null
    };
  }

  if (data.password !== '123456') {
    return {
      code: 400,
      message: '密码错误',
      data: null
    };
  }

  return {
    code: 200,
    message: '登录成功',
    data: {
      token: 'mock-jwt-token-' + Date.now(),
      userInfo: user
    }
  };
};

// 模拟注册API
export const mockRegister = async (data: RegisterForm): Promise<ApiResponse<void>> => {
  await delay(1000);

  // 检查用户名是否已存在
  const existingUser = mockUsers.find(u => u.username === data.username);
  if (existingUser) {
    return {
      code: 400,
      message: '用户名已存在',
      data: null
    };
  }

  return {
    code: 200,
    message: '注册成功',
    data: null
  };
};

// 模拟登出API
export const mockLogout = async (): Promise<ApiResponse<void>> => {
  await delay(500);
  
  return {
    code: 200,
    message: '登出成功',
    data: null
  };
};

// 检查后端是否可用
export const checkBackendAvailable = async (): Promise<boolean> => {
  try {
    const response = await fetch('http://localhost:8080/api/health', {
      method: 'GET',
      timeout: 3000
    } as any);
    return response.ok;
  } catch (error) {
    return false;
  }
};

// 模拟用户列表数据
export const mockUserList = {
  records: [
    {
      id: 1,
      username: 'admin',
      realName: '系统管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      role: 'SUPER_ADMIN',
      status: 1,
      createdTime: '2025-06-08 01:35:59',
      lastLoginTime: '2025-06-08 04:53:07'
    },
    {
      id: 2,
      username: 'manager',
      realName: '图书管理员',
      email: '<EMAIL>',
      phone: '13800138001',
      role: 'ADMIN',
      status: 1,
      createdTime: '2025-06-07 10:20:30',
      lastLoginTime: '2025-06-07 16:45:22'
    },
    {
      id: 3,
      username: 'user001',
      realName: '张三',
      email: '<EMAIL>',
      phone: '13800138002',
      role: 'USER',
      status: 1,
      createdTime: '2025-06-06 14:15:10',
      lastLoginTime: '2025-06-07 09:30:15'
    },
    {
      id: 4,
      username: 'user002',
      realName: '李四',
      email: '<EMAIL>',
      phone: '13800138003',
      role: 'USER',
      status: 0,
      createdTime: '2025-06-05 11:25:45',
      lastLoginTime: null
    }
  ],
  total: 4,
  current: 1,
  size: 10,
  pages: 1
};

// 模拟图书列表数据
export const mockBookList = {
  records: [
    {
      id: 1,
      isbn: '9787111544937',
      title: 'Java核心技术',
      author: 'Cay S. Horstmann',
      publisher: '机械工业出版社',
      publishDate: '2020-01-01',
      categoryId: 1,
      categoryName: '计算机技术',
      description: 'Java编程经典教材',
      coverUrl: null,
      price: 89.00,
      totalQuantity: 10,
      availableQuantity: 7,
      status: 1,
      createdTime: '2025-06-01 10:00:00',
      updatedTime: '2025-06-08 10:00:00'
    },
    {
      id: 2,
      isbn: '9787115428028',
      title: 'Spring Boot实战',
      author: 'Craig Walls',
      publisher: '人民邮电出版社',
      publishDate: '2019-05-01',
      categoryId: 1,
      categoryName: '计算机技术',
      description: 'Spring Boot开发指南',
      coverUrl: null,
      price: 79.00,
      totalQuantity: 8,
      availableQuantity: 5,
      status: 1,
      createdTime: '2025-06-02 10:00:00',
      updatedTime: '2025-06-08 10:00:00'
    }
  ],
  total: 2,
  current: 1,
  size: 10,
  pages: 1
};

// 模拟仪表盘统计数据
export const mockDashboardStats = {
  userStats: {
    totalUsers: 156,
    activeUsers: 142,
    newUsersToday: 3,
    newUsersThisMonth: 28
  },
  bookStats: {
    totalBooks: 1250,
    availableBooks: 980,
    borrowedBooks: 245,
    newBooksToday: 2,
    newBooksThisMonth: 45
  },
  borrowStats: {
    totalBorrows: 3420,
    activeBorrows: 245,
    overdueCount: 25,
    returnedToday: 12,
    borrowedToday: 8
  },
  systemStats: {
    totalCategories: 12,
    totalFines: 1250.50,
    systemUptime: '15天 8小时 32分钟',
    lastBackupTime: '2025-06-08 02:00:00'
  }
};

// 模拟借阅记录数据
export const mockBorrowList = {
  records: [
    {
      id: 1,
      userId: 3,
      username: 'user001',
      userRealName: '张三',
      bookId: 1,
      bookTitle: 'Java核心技术',
      bookAuthor: 'Cay S. Horstmann',
      bookIsbn: '9787111544937',
      borrowTime: '2025-06-01 10:00:00',
      dueTime: '2025-06-31 10:00:00',
      returnTime: null,
      status: 'BORROWED',
      overdueDays: 0,
      fineAmount: 0,
      remarks: null,
      createdTime: '2025-06-01 10:00:00',
      updatedTime: '2025-06-01 10:00:00'
    },
    {
      id: 2,
      userId: 4,
      username: 'user002',
      userRealName: '李四',
      bookId: 2,
      bookTitle: 'Spring Boot实战',
      bookAuthor: 'Craig Walls',
      bookIsbn: '9787115428028',
      borrowTime: '2025-05-15 14:30:00',
      dueTime: '2025-06-15 14:30:00',
      returnTime: '2025-06-05 16:20:00',
      status: 'RETURNED',
      overdueDays: 0,
      fineAmount: 0,
      remarks: '按时归还',
      createdTime: '2025-05-15 14:30:00',
      updatedTime: '2025-06-05 16:20:00'
    }
  ],
  total: 2,
  current: 1,
  size: 10,
  pages: 1
};
