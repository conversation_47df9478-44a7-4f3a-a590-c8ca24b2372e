import request from '@/utils/request';
import type { ApiResponse, LoginForm, RegisterForm, LoginResponse } from '@/types';
import { mockLogin, mockRegister, mockLogout, checkBackendAvailable } from './mock';

// 用户登录
export const login = async (data: LoginForm): Promise<ApiResponse<LoginResponse>> => {
  try {
    // 优先尝试调用真实后端API
    console.log('尝试连接真实后端API...');
    const response = await request.post('/auth/login', data);
    console.log('真实后端API响应:', response);
    return response;
  } catch (error: any) {
    console.warn('后端API调用失败，使用模拟数据:', error.message);
    // 后端不可用时使用模拟数据
    return await mockLogin(data);
  }
};

// 用户注册
export const register = async (data: RegisterForm): Promise<ApiResponse<void>> => {
  try {
    return await request.post('/auth/register', data);
  } catch (error) {
    console.warn('后端不可用，使用模拟数据:', error);
    return await mockRegister(data);
  }
};

// 用户登出
export const logout = async (): Promise<ApiResponse<void>> => {
  try {
    return await request.post('/auth/logout');
  } catch (error) {
    console.warn('后端不可用，使用模拟数据:', error);
    return await mockLogout();
  }
};
