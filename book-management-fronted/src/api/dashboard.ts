import request from '@/utils/request';
import type { ApiResponse } from '@/types';
import { mockDashboardStats } from './mock';

// 仪表盘API接口

// 获取仪表盘统计数据
export const getDashboardStats = async (): Promise<ApiResponse<{
  userStats: {
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    newUsersThisMonth: number;
  };
  bookStats: {
    totalBooks: number;
    availableBooks: number;
    borrowedBooks: number;
    newBooksToday: number;
    newBooksThisMonth: number;
  };
  borrowStats: {
    totalBorrows: number;
    activeBorrows: number;
    overdueCount: number;
    returnedToday: number;
    borrowedToday: number;
  };
  systemStats: {
    totalCategories: number;
    totalFines: number;
    systemUptime: string;
    lastBackupTime: string;
  };
}>> => {
  try {
    return await request.get('/dashboard/stats');
  } catch (error) {
    console.warn('获取仪表盘数据失败，使用模拟数据:', error);
    // 使用模拟数据
    return {
      code: 200,
      message: '获取成功',
      data: mockDashboardStats
    };
  }
};

// 获取借阅趋势数据（最近30天）
export const getBorrowTrend = (): Promise<ApiResponse<{
  dates: string[];
  borrowCounts: number[];
  returnCounts: number[];
}>> => {
  return request.get('/dashboard/borrow-trend');
};

// 获取热门图书排行
export const getPopularBooks = (limit: number = 10): Promise<ApiResponse<{
  id: number;
  title: string;
  author: string;
  borrowCount: number;
  coverUrl?: string;
}[]>> => {
  return request.get('/dashboard/popular-books', { params: { limit } });
};

// 获取活跃用户排行
export const getActiveUsers = (limit: number = 10): Promise<ApiResponse<{
  id: number;
  username: string;
  realName: string;
  borrowCount: number;
  avatar?: string;
}[]>> => {
  return request.get('/dashboard/active-users', { params: { limit } });
};

// 获取图书分类统计
export const getCategoryStats = (): Promise<ApiResponse<{
  categoryName: string;
  bookCount: number;
  borrowCount: number;
}[]>> => {
  return request.get('/dashboard/category-stats');
};

// 获取最近活动记录
export const getRecentActivities = (limit: number = 20): Promise<ApiResponse<{
  id: number;
  type: 'BORROW' | 'RETURN' | 'USER_REGISTER' | 'BOOK_ADD';
  description: string;
  userName?: string;
  bookTitle?: string;
  createTime: string;
}[]>> => {
  return request.get('/dashboard/recent-activities', { params: { limit } });
};

// 获取系统健康状态
export const getSystemHealth = (): Promise<ApiResponse<{
  database: 'HEALTHY' | 'WARNING' | 'ERROR';
  redis: 'HEALTHY' | 'WARNING' | 'ERROR';
  storage: 'HEALTHY' | 'WARNING' | 'ERROR';
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
}>> => {
  return request.get('/dashboard/system-health');
};
