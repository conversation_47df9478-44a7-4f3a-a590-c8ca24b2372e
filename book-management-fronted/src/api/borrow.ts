import request from '@/utils/request';
import type { ApiResponse, BorrowRecord, BorrowForm, PageResult } from '@/types';

// 借阅管理API接口

// 获取借阅记录列表（分页）
export const getBorrowList = (params: {
  current: number;
  size: number;
  keyword?: string;
  status?: string;
  userId?: number;
  bookId?: number;
  startDate?: string;
  endDate?: string;
}): Promise<ApiResponse<PageResult<BorrowRecord>>> => {
  return request.get('/borrow/records', { params });
};

// 根据ID获取借阅记录详情
export const getBorrowById = (id: number): Promise<ApiResponse<BorrowRecord>> => {
  return request.get(`/borrow/records/${id}`);
};

// 创建借阅记录（借书）
export const createBorrow = (data: BorrowForm): Promise<ApiResponse<void>> => {
  return request.post('/borrow/borrow', data);
};

// 归还图书
export const returnBook = (id: number, remarks?: string): Promise<ApiResponse<void>> => {
  return request.post(`/borrow/return/${id}`, { remarks });
};

// 续借图书
export const renewBook = (id: number, days: number): Promise<ApiResponse<void>> => {
  return request.post(`/borrow/renew/${id}`, { days });
};

// 批量归还
export const batchReturnBooks = (ids: number[]): Promise<ApiResponse<void>> => {
  return request.put('/borrow/batch-return', { ids });
};

// 获取用户借阅历史
export const getUserBorrowHistory = (userId: number, params: {
  current: number;
  size: number;
  status?: string;
}): Promise<ApiResponse<PageResult<BorrowRecord>>> => {
  return request.get(`/borrow/user/${userId}`, { params });
};

// 获取图书借阅历史
export const getBookBorrowHistory = (bookId: number, params: {
  current: number;
  size: number;
  status?: string;
}): Promise<ApiResponse<PageResult<BorrowRecord>>> => {
  return request.get(`/borrow/book/${bookId}`, { params });
};

// 获取逾期记录
export const getOverdueRecords = (params: {
  current: number;
  size: number;
  keyword?: string;
}): Promise<ApiResponse<PageResult<BorrowRecord>>> => {
  return request.get('/borrow/overdue', { params });
};

// 处理逾期罚款
export const processFine = (id: number, amount: number): Promise<ApiResponse<void>> => {
  return request.put(`/borrow/${id}/fine`, { amount });
};

// 获取借阅统计数据
export const getBorrowStatistics = (): Promise<ApiResponse<{
  totalBorrows: number;
  activeBorrows: number;
  overdueCount: number;
  returnedCount: number;
  totalFines: number;
}>> => {
  return request.get('/borrow/statistics');
};
