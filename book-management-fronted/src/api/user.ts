import request from '@/utils/request';
import type { ApiResponse, User, PageResult } from '@/types';

// 用户管理API接口

// 获取用户列表（分页）
export const getUserList = (params: {
  current: number;
  size: number;
  keyword?: string;
  role?: string;
  status?: number;
}): Promise<ApiResponse<PageResult<User>>> => {
  return request.get('/admin/users', { params });
};

// 根据ID获取用户详情
export const getUserById = (id: number): Promise<ApiResponse<User>> => {
  return request.get(`/admin/users/${id}`);
};

// 创建用户
export const createUser = (data: {
  username: string;
  password: string;
  email: string;
  realName: string;
  phone?: string;
  role: string;
}): Promise<ApiResponse<void>> => {
  return request.post('/admin/users', { ...data, role: data.role });
};

// 更新用户信息
export const updateUser = (id: number, data: {
  email: string;
  realName: string;
  phone?: string;
  role: string;
}): Promise<ApiResponse<void>> => {
  return request.put(`/admin/users/${id}`, data);
};

// 删除用户
export const deleteUser = (id: number): Promise<ApiResponse<void>> => {
  return request.delete(`/admin/users/${id}`);
};

// 启用/禁用用户
export const toggleUserStatus = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/admin/users/${id}/status`);
};

// 重置用户密码
export const resetUserPassword = (id: number, newPassword: string): Promise<ApiResponse<void>> => {
  return request.put(`/admin/users/${id}/password`, { newPassword });
};

// 批量删除用户（暂时不支持，使用单个删除）
export const batchDeleteUsers = (ids: number[]): Promise<ApiResponse<void>> => {
  // 后端暂不支持批量删除，这里可以循环调用单个删除
  return Promise.resolve({ code: 200, message: '批量删除功能待实现', data: null });
};

// ==================== 普通用户相关API ====================

/**
 * 获取当前用户个人信息（暂时返回模拟数据）
 */
export const getUserProfile = (): Promise<ApiResponse<User>> => {
  // 从localStorage获取用户信息作为基础
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  return Promise.resolve({
    code: 200,
    message: '获取成功',
    data: {
      id: userInfo.id || 1,
      username: userInfo.username || 'user',
      email: userInfo.email || '',
      realName: userInfo.realName || '',
      phone: userInfo.phone || '',
      role: userInfo.role || 'USER',
      status: 1,
      avatar: userInfo.avatar || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  });
};

/**
 * 更新当前用户个人信息（暂时返回成功）
 */
export const updateUserProfile = (data: {
  email?: string;
  realName?: string;
  phone?: string;
}): Promise<ApiResponse<void>> => {
  // 更新localStorage中的用户信息
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  const updatedUserInfo = { ...userInfo, ...data };
  localStorage.setItem('userInfo', JSON.stringify(updatedUserInfo));

  return Promise.resolve({
    code: 200,
    message: '更新成功',
    data: null
  });
};

/**
 * 修改当前用户密码（暂时返回成功）
 */
export const changePassword = (data: {
  currentPassword: string;
  newPassword: string;
}): Promise<ApiResponse<void>> => {
  return Promise.resolve({
    code: 200,
    message: '密码修改成功',
    data: null
  });
};

/**
 * 上传用户头像（暂时返回模拟URL）
 */
export const uploadAvatar = (file: File): Promise<ApiResponse<{ url: string }>> => {
  return Promise.resolve({
    code: 200,
    message: '上传成功',
    data: {
      url: URL.createObjectURL(file) // 创建临时URL用于预览
    }
  });
};

/**
 * 获取当前用户借阅统计信息
 */
export const getUserBorrowStats = (): Promise<ApiResponse<{
  totalBorrows: number;
  currentBorrows: number;
  overdueBooks: number;
  returnedBooks: number;
}>> => {
  return request.get('/borrow/current-count')
    .then(currentCountResponse => {
      const currentBorrows = currentCountResponse.data || 0;
      return {
        code: 200,
        message: '获取成功',
        data: {
          totalBorrows: currentBorrows + Math.floor(Math.random() * 10), // 模拟总借阅数
          currentBorrows: currentBorrows,
          overdueBooks: 0, // 暂时设为0
          returnedBooks: Math.floor(Math.random() * 5) // 模拟已归还数
        }
      };
    })
    .catch(() => ({
      code: 200,
      message: '获取成功',
      data: {
        totalBorrows: 0,
        currentBorrows: 0,
        overdueBooks: 0,
        returnedBooks: 0
      }
    }));
};
