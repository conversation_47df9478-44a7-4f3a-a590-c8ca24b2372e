import request from '@/utils/request';

// 收藏操作接口
export interface FavoriteRequest {
  bookId: number;
  action?: boolean; // true-添加收藏，false-取消收藏
}

// 收藏响应接口
export interface FavoriteResponse {
  isFavorited: boolean;
  message: string;
}

// 收藏列表项接口
export interface UserFavoriteItem {
  id: number;
  userId: number;
  username: string;
  userRealName: string;
  bookId: number;
  bookTitle: string;
  bookAuthor: string;
  bookIsbn: string;
  bookCategory: string;
  bookCoverUrl?: string;
  bookStatus: number;
  bookAvailableQuantity: number;
  favoriteTime: string;
  createdTime: string;
  // 前端状态
  removing?: boolean;
}

// 分页收藏列表响应
export interface FavoriteListResponse {
  records: UserFavoriteItem[];
  total: number;
  current: number;
  size: number;
  pages: number;
}

// 收藏统计信息
export interface FavoriteStats {
  totalCount: number;
  userId: number;
}

/**
 * 添加收藏
 */
export const addFavorite = (bookId: number) => {
  console.log('🔖 API调用 - 添加收藏:', bookId);
  return request.post<string>('/user/favorites/add', { bookId });
};

/**
 * 取消收藏
 */
export const removeFavorite = (bookId: number) => {
  console.log('🔖 API调用 - 取消收藏:', bookId);
  return request.post<string>('/user/favorites/remove', { bookId });
};

/**
 * 切换收藏状态
 */
export const toggleFavorite = (bookId: number) => {
  console.log('🔖 API调用 - 切换收藏状态:', bookId);
  return request.post<FavoriteResponse>('/user/favorites/toggle', { bookId });
};

/**
 * 检查收藏状态
 */
export const checkFavoriteStatus = (bookId: number) => {
  console.log('🔖 API调用 - 检查收藏状态:', bookId);
  return request.get<boolean>(`/user/favorites/check/${bookId}`);
};

/**
 * 分页查询用户收藏列表
 */
export const getUserFavoriteList = (params: {
  current?: number;
  size?: number;
}) => {
  console.log('🔖 API调用 - 查询收藏列表:', params);
  return request.get<FavoriteListResponse>('/user/favorites/list', { params });
};

/**
 * 获取用户收藏的图书ID列表
 */
export const getUserFavoriteBookIds = () => {
  console.log('🔖 API调用 - 查询收藏图书ID列表');
  return request.get<number[]>('/user/favorites/book-ids');
};

/**
 * 批量查询图书收藏状态
 */
export const batchCheckFavoriteStatus = (bookIds: number[]) => {
  console.log('🔖 API调用 - 批量查询收藏状态:', bookIds);
  return request.post<Record<number, boolean>>('/user/favorites/batch-check', bookIds);
};

/**
 * 获取用户收藏统计信息
 */
export const getUserFavoriteStats = () => {
  console.log('🔖 API调用 - 查询收藏统计');
  return request.get<FavoriteStats>('/user/favorites/stats');
};

/**
 * 获取图书收藏数量
 */
export const getBookFavoriteCount = (bookId: number) => {
  console.log('🔖 API调用 - 查询图书收藏数量:', bookId);
  return request.get<number>(`/user/favorites/book/${bookId}/count`);
};
