import request from '@/utils/request';
import type { ApiResponse, Book, BookForm, PageResult, BookCategory } from '@/types';

// 图书管理API接口

// 获取图书列表（分页）
export const getBookList = (params: {
  current: number;
  size: number;
  keyword?: string;
  categoryId?: number;
  status?: number;
}): Promise<ApiResponse<PageResult<Book>>> => {
  return request.get('/books', { params });
};

// 根据ID获取图书详情
export const getBookById = (id: number): Promise<ApiResponse<Book>> => {
  return request.get(`/books/${id}`);
};

// 创建图书
export const createBook = (data: BookForm): Promise<ApiResponse<void>> => {
  return request.post('/books', data);
};

// 更新图书信息
export const updateBook = (id: number, data: BookForm): Promise<ApiResponse<void>> => {
  return request.put(`/books/${id}`, data);
};

// 删除图书
export const deleteBook = (id: number): Promise<ApiResponse<void>> => {
  return request.delete(`/books/${id}`);
};

// 批量删除图书（暂时不支持）
export const batchDeleteBooks = (ids: number[]): Promise<ApiResponse<void>> => {
  return Promise.resolve({ code: 200, message: '批量删除功能待实现', data: null });
};

// 上架/下架图书
export const toggleBookStatus = (id: number): Promise<ApiResponse<void>> => {
  return request.put(`/books/${id}/status`);
};

// 获取图书分类列表
export const getBookCategories = (): Promise<ApiResponse<BookCategory[]>> => {
  return request.get('/categories');
};

// 创建图书分类
export const createBookCategory = (data: {
  name: string;
  parentId: number;
  description?: string;
  sortOrder?: number;
}): Promise<ApiResponse<void>> => {
  return request.post('/book/category', data);
};

// 更新图书分类
export const updateBookCategory = (id: number, data: {
  name: string;
  parentId: number;
  description?: string;
  sortOrder?: number;
}): Promise<ApiResponse<void>> => {
  return request.put(`/book/category/${id}`, data);
};

// 删除图书分类
export const deleteBookCategory = (id: number): Promise<ApiResponse<void>> => {
  return request.delete(`/book/category/${id}`);
};

// 根据ISBN查询图书
export const getBookByIsbn = (isbn: string): Promise<ApiResponse<Book>> => {
  return request.get(`/book/isbn/${isbn}`);
};

// ==================== 用户端图书相关API ====================

/**
 * 用户端获取图书列表（支持搜索和筛选）
 */
export const getUserBookList = (params: {
  current: number;
  size: number;
  keyword?: string;
  category?: string;
  status?: string; // AVAILABLE, BORROWED
  author?: string;
  publisher?: string;
}): Promise<ApiResponse<PageResult<Book>>> => {
  // 使用现有的图书列表接口，将category转换为categoryId
  let statusValue: number | undefined = undefined;

  if (params.status === 'AVAILABLE') {
    statusValue = 1;
  } else if (params.status === 'BORROWED') {
    statusValue = 0;
  }
  // 如果没有指定status，保持undefined，让后端返回所有状态的图书

  const apiParams = {
    current: params.current,
    size: params.size,
    keyword: params.keyword,
    categoryId: undefined as number | undefined,
    status: statusValue
  };

  console.log('📚 getUserBookList API参数:', {
    原始params: params,
    转换后apiParams: apiParams,
    状态转换: `"${params.status}" -> ${statusValue}`
  });

  return request.get('/books', { params: apiParams });
};

/**
 * 用户端搜索图书
 */
export const searchBooks = (params: {
  keyword?: string;
  category?: string;
  author?: string;
  publisher?: string;
  isbn?: string;
  status?: string;
  current?: number;
  size?: number;
}): Promise<ApiResponse<PageResult<Book>>> => {
  // 使用现有的图书列表接口进行搜索
  const apiParams = {
    current: params.current || 1,
    size: params.size || 10,
    keyword: params.keyword,
    categoryId: undefined as number | undefined,
    status: params.status === 'AVAILABLE' ? 1 : params.status === 'BORROWED' ? 0 : undefined
  };
  return request.get('/books', { params: apiParams });
};

/**
 * 获取图书详情（用户端）
 */
export const getUserBookDetail = (id: number): Promise<ApiResponse<Book>> => {
  return request.get(`/books/${id}`);
};

/**
 * 获取热门图书（暂时使用图书列表接口）
 */
export const getPopularBooks = (limit: number = 10): Promise<ApiResponse<Book[]>> => {
  return request.get('/books', { params: { current: 1, size: limit } })
    .then(response => ({
      ...response,
      data: response.data.records || []
    }));
};

/**
 * 获取最新图书（暂时使用图书列表接口）
 */
export const getLatestBooks = (limit: number = 10): Promise<ApiResponse<Book[]>> => {
  return request.get('/books', { params: { current: 1, size: limit } })
    .then(response => ({
      ...response,
      data: response.data.records || []
    }));
};

/**
 * 获取图书分类列表（用户端）
 */
export const getUserBookCategories = (): Promise<ApiResponse<string[]>> => {
  return request.get('/categories')
    .then(response => {
      console.log('📂 分类API原始响应:', response);

      if (response.code === 200 && response.data) {
        let categories: string[] = [];

        if (Array.isArray(response.data)) {
          // 如果是数组，提取分类名称
          categories = response.data.map((category: any) => {
            if (typeof category === 'string') {
              return category;
            } else if (category && typeof category === 'object') {
              return category.name || category.categoryName || category.value || String(category);
            }
            return String(category);
          }).filter(Boolean);
        } else if (typeof response.data === 'string') {
          // 如果是字符串，按逗号分割
          categories = response.data.split(',').filter(Boolean);
        }

        console.log('📂 处理后的分类数据:', categories);
        return {
          ...response,
          data: categories
        };
      }

      return {
        ...response,
        data: []
      };
    })
    .catch(error => {
      console.error('📂 获取分类失败:', error);
      return {
        code: 500,
        message: '获取分类失败',
        data: []
      };
    });
};
