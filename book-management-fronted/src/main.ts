import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import pinia from './stores';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import './style.css';
import { useUserStore } from '@/stores/user';
import { permission } from './directives/permission';

console.log('Starting application...');

const app = createApp(App);

try {
  app.use(pinia);
  app.use(router);
  app.use(Antd);

  // 注册权限指令
  app.directive('permission', permission);

  // 初始化用户状态
  const userStore = useUserStore();
  userStore.initUserInfo();

  app.mount('#app');
  console.log('Application mounted successfully');
} catch (error) {
  console.error('Failed to mount application:', error);
}
