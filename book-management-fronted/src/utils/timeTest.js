// 时间计算测试脚本
console.log('=== 时间计算测试 ===');

// 模拟您的数据库记录
const dbRecord = {
  borrowTime: '2025-06-08 21:46:03',
  dueTime: '2025-07-08 21:46:03',
  today: '2025-06-08'
};

console.log('数据库记录:', dbRecord);

// 测试不同的时间格式转换
const testTimeFormats = [
  '2025-07-08 21:46:03',
  '2025-07-08T21:46:03',
  '2025-07-08T21:46:03.000Z',
  new Date('2025-07-08 21:46:03').toISOString(),
  [2025, 7, 8, 21, 46, 3] // LocalDateTime数组格式
];

console.log('\n=== 时间格式转换测试 ===');
testTimeFormats.forEach((format, index) => {
  console.log(`格式 ${index + 1}:`, format);
  try {
    let date;
    if (Array.isArray(format)) {
      const [year, month, day, hour, minute, second] = format;
      date = new Date(year, month - 1, day, hour, minute, second);
    } else {
      date = new Date(format);
    }
    console.log('  转换结果:', date.toISOString());
    console.log('  是否有效:', !isNaN(date.getTime()));
  } catch (error) {
    console.log('  转换失败:', error.message);
  }
  console.log('');
});

// 测试剩余天数计算
console.log('\n=== 剩余天数计算测试 ===');

const calculateRemainingDays = (dueDate) => {
  if (!dueDate) return 0;
  
  try {
    const due = new Date(dueDate);
    const now = new Date('2025-06-08'); // 固定今天的日期
    
    if (isNaN(due.getTime())) return 0;
    
    // 设置时间为当天的开始，避免时间部分影响计算
    const dueDay = new Date(due.getFullYear(), due.getMonth(), due.getDate());
    const nowDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    const diffTime = dueDay.getTime() - nowDay.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    console.log('计算详情:');
    console.log('  到期日期:', dueDay.toISOString());
    console.log('  当前日期:', nowDay.toISOString());
    console.log('  时间差(ms):', diffTime);
    console.log('  剩余天数:', diffDays);
    
    return diffDays;
  } catch (error) {
    console.error('计算剩余天数时出错:', error);
    return 0;
  }
};

// 测试您的具体数据
const testCases = [
  {
    name: '您的数据库记录',
    dueDate: '2025-07-08 21:46:03',
    expected: 30
  },
  {
    name: '今日到期',
    dueDate: '2025-06-08 23:59:59',
    expected: 0
  },
  {
    name: '明日到期',
    dueDate: '2025-06-09 00:00:00',
    expected: 1
  },
  {
    name: '逾期1天',
    dueDate: '2025-06-07 12:00:00',
    expected: -1
  }
];

testCases.forEach(testCase => {
  console.log(`\n测试: ${testCase.name}`);
  console.log(`  到期日期: ${testCase.dueDate}`);
  console.log(`  期望结果: ${testCase.expected}天`);
  
  const result = calculateRemainingDays(testCase.dueDate);
  console.log(`  实际结果: ${result}天`);
  console.log(`  测试结果: ${result === testCase.expected ? '✅ 通过' : '❌ 失败'}`);
});

console.log('\n=== 测试完成 ===');
