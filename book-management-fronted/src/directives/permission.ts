import type { Directive, DirectiveBinding } from 'vue';
import { useUserStore } from '@/stores/user';

/**
 * 权限控制指令
 * 用法：
 * v-permission="'ADMIN'" - 单个角色
 * v-permission="['ADMIN', 'SUPER_ADMIN']" - 多个角色
 * v-permission:hide="'ADMIN'" - 隐藏元素而不是移除
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding);
  }
};

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const userStore = useUserStore();
  const { value, arg } = binding;
  
  // 如果没有指定权限要求，直接显示
  if (!value) {
    return;
  }
  
  // 获取用户角色
  const userRole = userStore.userInfo?.role;
  if (!userRole) {
    // 用户未登录，隐藏或移除元素
    handleNoPermission(el, arg);
    return;
  }
  
  // 超级管理员拥有所有权限
  if (userRole === 'SUPER_ADMIN') {
    showElement(el);
    return;
  }
  
  // 检查权限
  const requiredRoles = Array.isArray(value) ? value : [value];
  const hasPermission = requiredRoles.includes(userRole);
  
  if (hasPermission) {
    showElement(el);
  } else {
    handleNoPermission(el, arg);
  }
}

function showElement(el: HTMLElement) {
  // 恢复元素显示
  if (el.style.display === 'none') {
    el.style.display = '';
  }
  
  // 如果元素被移除了，需要重新添加（这种情况比较复杂，通常不推荐）
  // 这里只处理隐藏的情况
}

function handleNoPermission(el: HTMLElement, arg?: string) {
  if (arg === 'hide') {
    // 隐藏元素
    el.style.display = 'none';
  } else {
    // 移除元素
    el.parentNode?.removeChild(el);
  }
}

/**
 * 权限检查函数，可以在组件中使用
 */
export function hasPermission(requiredRole: string | string[]): boolean {
  const userStore = useUserStore();
  return userStore.hasPermission(requiredRole);
}

/**
 * 角色检查函数
 */
export function hasRole(role: string): boolean {
  const userStore = useUserStore();
  const userRole = userStore.userInfo?.role;
  
  if (!userRole) return false;
  if (userRole === 'SUPER_ADMIN') return true;
  
  return userRole === role;
}

/**
 * 检查是否可以访问管理功能
 */
export function canAccessAdmin(): boolean {
  const userStore = useUserStore();
  return userStore.canAccessAdmin;
}

/**
 * 检查是否可以访问用户管理
 */
export function canAccessUserManagement(): boolean {
  const userStore = useUserStore();
  return userStore.canAccessUserManagement;
}

/**
 * 检查是否可以访问图书管理
 */
export function canAccessBookManagement(): boolean {
  const userStore = useUserStore();
  return userStore.canAccessBookManagement;
}

/**
 * 检查是否可以访问借阅管理
 */
export function canAccessBorrowManagement(): boolean {
  const userStore = useUserStore();
  return userStore.canAccessBorrowManagement;
}

export default permission;
