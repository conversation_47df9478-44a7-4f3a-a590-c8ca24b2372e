import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { User, LoginForm, RegisterForm, LoginResponse } from '@/types';
import { login, register } from '@/api/auth';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '');
  const userInfo = ref<User | null>(null);
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value);
  const isUser = computed(() => userInfo.value?.role === 'USER');
  const isAdmin = computed(() => {
    return userInfo.value?.role === 'ADMIN' || userInfo.value?.role === 'SUPER_ADMIN';
  });
  const isSuperAdmin = computed(() => {
    return userInfo.value?.role === 'SUPER_ADMIN';
  });
  const isRegularAdmin = computed(() => userInfo.value?.role === 'ADMIN');

  // 权限检查方法
  const hasPermission = (requiredRole: string | string[]): boolean => {
    if (!userInfo.value?.role) return false;

    const userRole = userInfo.value.role;
    const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

    // 超级管理员拥有所有权限
    if (userRole === 'SUPER_ADMIN') return true;

    // 检查是否包含所需角色
    return roles.includes(userRole);
  };

  const canAccessAdmin = computed(() => hasPermission(['ADMIN', 'SUPER_ADMIN']));
  const canAccessUserManagement = computed(() => hasPermission('SUPER_ADMIN'));
  const canAccessBookManagement = computed(() => hasPermission(['ADMIN', 'SUPER_ADMIN']));
  const canAccessBorrowManagement = computed(() => hasPermission(['ADMIN', 'SUPER_ADMIN']));
  
  // 登录
  const loginAction = async (loginForm: LoginForm): Promise<void> => {
    try {
      console.log('开始登录请求:', loginForm.username);
      const response = await login(loginForm);
      console.log('登录响应:', response);

      if (response.code !== 200) {
        throw new Error(response.message || '登录失败');
      }

      const { token: newToken, user: newUserInfo } = response.data;
      console.log('解析登录数据:', { token: newToken, user: newUserInfo });

      token.value = newToken;
      userInfo.value = newUserInfo;

      // 保存到本地存储
      localStorage.setItem('token', newToken);
      localStorage.setItem('userInfo', JSON.stringify(newUserInfo));

      console.log('登录成功，用户信息已保存:', {
        token: newToken.substring(0, 20) + '...',
        userRole: newUserInfo.role,
        userId: newUserInfo.id
      });
    } catch (error: any) {
      console.error('登录失败:', error);
      // 如果是axios错误，提取后端返回的错误信息
      if (error.response && error.response.data && error.response.data.message) {
        throw new Error(error.response.data.message);
      }
      // 如果是其他错误，直接抛出
      throw error;
    }
  };
  
  // 注册
  const registerAction = async (registerForm: RegisterForm): Promise<void> => {
    try {
      await register(registerForm);
    } catch (error) {
      throw error;
    }
  };
  
  // 登出
  const logout = (): void => {
    token.value = '';
    userInfo.value = null;
    
    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
  };
  
  // 初始化用户信息
  const initUserInfo = (): void => {
    const storedUserInfo = localStorage.getItem('userInfo');
    const storedToken = localStorage.getItem('token');

    if (storedUserInfo && storedUserInfo !== 'undefined' && storedToken) {
      try {
        userInfo.value = JSON.parse(storedUserInfo);
        token.value = storedToken;
      } catch (error) {
        console.error('解析用户信息失败:', error);
        logout();
      }
    } else {
      // 如果存储的数据无效，清除所有数据
      logout();
    }
  };
  
  // 更新用户信息
  const updateUserInfo = (newUserInfo: User): void => {
    userInfo.value = newUserInfo;
    localStorage.setItem('userInfo', JSON.stringify(newUserInfo));
  };

  // 设置token
  const setToken = (newToken: string): void => {
    token.value = newToken;
    localStorage.setItem('token', newToken);
  };

  // 设置用户信息
  const setUserInfo = (newUserInfo: User): void => {
    userInfo.value = newUserInfo;
    localStorage.setItem('userInfo', JSON.stringify(newUserInfo));
  };

  return {
    // 状态
    token,
    userInfo,

    // 计算属性
    isLoggedIn,
    isUser,
    isAdmin,
    isSuperAdmin,
    isRegularAdmin,
    canAccessAdmin,
    canAccessUserManagement,
    canAccessBookManagement,
    canAccessBorrowManagement,

    // 方法
    loginAction,
    registerAction,
    logout,
    initUserInfo,
    updateUserInfo,
    setToken,
    setUserInfo,
    hasPermission
  };
});
